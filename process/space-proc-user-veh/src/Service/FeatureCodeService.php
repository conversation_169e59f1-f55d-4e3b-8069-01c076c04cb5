<?php 

namespace App\Service;

use App\Helper\VehicleTypeEntities;
use App\Model\FeatureCode;
use App\Connector\SystemIdpConnector;
use Symfony\Component\Yaml\Yaml;
use App\Trait\LoggerTrait;
use App\Service\UserDataService;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use App\Helper\WSResponse;
use App\Model\FeatureCodeConfig;
use Symfony\Component\HttpFoundation\Response;
use Space\MongoDocuments\Repository\SPSEligibilityRepository;

class FeatureCodeService
{
    use LoggerTrait;

    private string $cdnUrl;
    
    public function __construct(
        private UserDataService $userDataService,
        private HttpClientInterface $client,
        private SystemIdpConnector $systemIdpConnector,
        private SPSEligibilityRepository $spsEligibilityRepository,
        string $cdnUrl,
    )
    {
        $this->cdnUrl = $cdnUrl;
    }
    
    private function isSps(string $lcdv, array $attributes): bool
    {
        $lcdvDigit = substr($lcdv, 0, 4);
        $dlx = VehicleTypeEntities::getType('DLX', $attributes);
        $drc = VehicleTypeEntities::getType('DRC', $attributes);
        $dzz = VehicleTypeEntities::getType('DZZ', $attributes);

        return in_array($lcdvDigit, ['1IK9', '2IK9']) &&
            ($dlx == '00' && (in_array($drc, ['00', '71']) && $dzz == '01'));
    }

    /**
     * Generate the feature codes array for a given vehicle based on user ID, VIN, LCDV, 
     * engine type, and Corvet attributes.
     *
     * This function determines the applicable feature codes by evaluating the vehicle's 
     * specifications, including its LCDV, engine type, and Corvet attributes.
     *
     * @param string $userId             The unique identifier of the user.
     * @param string $vin                The Vehicle Identification Number (VIN).
     * @param string $lcdv               The LCDV value of the vehicle.
     * @param string $engineType         The type of engine (ICE, PHEV, BEV, etc.).
     * @param array  $corvertAttributs   The list of Corvet attributes associated with the vehicle.
     * @param mixed  $f2mcObj            An optional data related to Free2Move (F2MC) services from userData.
     * @param string $country            The country code for localization.   
     * @param bool   $includeNonFds      If false, only CVS service codes will be used to calculate feature codes. (Non FDS codes are not recalculated)
     * @param string $userDbId           The user database ID.
     * @return array The list of applicable feature codes.
     */
    public function getFeaturesCode(string $userId, string $vin, string $lcdv, string $engineType, array $corvertAttributs, $f2mcObj = null, $country = null, $includeNonFds = true, $userDbId = null)
    {
        // $feature['calcTimestamp'] = $timestamp;
        $this->logger->info("Starting getFeaturesCode", [
            "userId" => $userId,
            "vin" => $vin,
            "lcdv" => $lcdv,
            "engineType" => $engineType,
            "corvertAttributs" => $corvertAttributs,
            "f2mcObj" => $f2mcObj,
            "country" => $country,
            "userDbId" => $userDbId
        ]);
        
        try {
            // Load feature code config
            $featureCodeConfig = FeatureCodeConfig::getAllFeatureConfig();

            // Call the CVS consumer rights API to get the Service codes or FDS
            $serviceCodes = [];
            try {
                $serviceCodes = $this->getServiceCodesByVin($vin, $userDbId);
            } catch (\Exception $e) {
                $this->logger->warning(__CLASS__ . "::" . __METHOD__ . " Failed to get service codes, continuing with non-FDS features: " . $e->getMessage());
                // Continue with empty service codes - non-FDS features will still be added
            }
            // NOTE: Not add null check as there are non FDS FeatureCodes to be added as well

            // For testing purposes only - enable if consumer rights API is not working
            if (empty($serviceCodes)) {
                $this->logger->warning(__CLASS__ . "::" . __METHOD__ . " No service codes retrieved, using mock data for testing");
                $serviceCodes = $this->getMockServiceCodes();
            }

            $this->logger->info(__CLASS__ . "::" . __METHOD__."::Service codes retrieved in getFeaturesCode: ", ["serviceCodes" => $serviceCodes]);

            $featureCodes = $this->dynamicStatusConfigCalculation($featureCodeConfig, $serviceCodes, $lcdv, $engineType, $corvertAttributs, $f2mcObj, $country, $includeNonFds);
            $this->logger->info(__CLASS__ . "::" . __METHOD__."::Calculated feature codes: ", ["featureCodes" => $featureCodes]);
            return $featureCodes;
        } catch (\Exception $e) {
            $this->logger->error("Error in " . __CLASS__ . "::" . __METHOD__ . $e->getMessage());
            return [];
        }
    }

    /**
     * Retrieves the service codes associated with a specific VIN for a given user, from the CVS consumer rights API.
     *
     * @param string $vin The Vehicle Identification Number (VIN) to search for.
     * @param string $userDbId The user database ID for whom the data is retrieved.
     * @return array An array of service codes associated with the given VIN.
     */
    public function getServiceCodesByVin(string $vin, string $userDbId)
    {
        try {
            $consumerRightsData = $this->getConsumerRightsData($userDbId, $vin)->getData();
            $codes = [];

            foreach ($consumerRightsData["success"]["services"] as $service) {
                foreach ($service as $details) {
                    if (isset($details["code"])) {
                        $codes[] = $details["code"];
                    }
                }
            }
            $this->logger->info("Service codes retrieved for VIN: $vin" . implode(",", $codes));
            return $codes;
        } catch (\Exception $e) {
            $this->logger->error("Error in " . __CLASS__ . "::" . __METHOD__ . $e->getMessage());
            throw new \Exception($e->getMessage(), $e->getCode());
        }
    }

    public function getConsumerRightsData(string $userDBId, string $vin): WSResponse
    {
        try {
            $response = $this->systemIdpConnector->call('GET', '/v1/consumer-rights?vin=' . $vin . '&userDbId=' . $userDBId);
            if ($response->getCode() === 200) {
                return $response;
            }
            $this->logger->error("Error in " . __CLASS__ . "::" . __METHOD__ . $response->getData());
            $responseData = $response->getData();
            $result = (isset($responseData['error']['message'])) ? $responseData['error']['message'] : json_encode($responseData);
            $this->logger->error('=> ' . __METHOD__ . ' => error : ' . $result);

            throw new \Exception($result, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error("Error in " . __CLASS__ . "::" . __METHOD__ . $e->getMessage());
            throw new \Exception($e->getMessage(), $e->getCode());
        }
    }

    public function getMockConsumerRightsData(string $userID, string $vin): string {
        return json_encode([
            "customer_id" => "********************************",
            "event_id" => "some_event",
            "grants" => [
                [
                    "contract_id" => "contract123",
                "services" => [
                            ["auth_type" => "auth1", "code" => "NAE01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAK01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAL01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAM01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAO01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAO02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAS01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAS02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAU01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAW01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NBM01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NBM02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth2", "code" => "NBI01", "mqtt_organization" => "org2"],
                            ["auth_type" => "auth3", "code" => "NBG01", "mqtt_organization" => "org3"],
                            ["auth_type" => "auth2", "code" => "NCG01", "mqtt_organization" => "org2"],
                            ["auth_type" => "auth1", "code" => "NEE02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NEF01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NFC01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NFD01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAB01", "mqtt_organization" => "org1"]

                        ],
                        "uin" => "uin123",
                        "vin" => "ZARHBTTG3R901144Z"
                    ],
                    [
                        "contract_id" => "contract123",
                        "services" => [
                            ["auth_type" => "auth1", "code" => "NAE01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAK01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAL01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAM01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAO01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAO02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAS01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAS02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAU01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAW01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NBM01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NBM02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth2", "code" => "NBI01", "mqtt_organization" => "org2"],
                            ["auth_type" => "auth3", "code" => "NBG01", "mqtt_organization" => "org3"],
                            ["auth_type" => "auth2", "code" => "NCG01", "mqtt_organization" => "org2"],
                            ["auth_type" => "auth1", "code" => "NEE02", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NEF01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NFC01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NFD01", "mqtt_organization" => "org1"],
                            ["auth_type" => "auth1", "code" => "NAB01", "mqtt_organization" => "org1"]

                        ],
                        "uin" => "uin123",
                        "vin" => "VR3UPHNKSKT101600"
                    ],
                ],
                "vin" => "VIN123"
        ]);
    }

    /**
     * Calculates and assigns feature codes based on various conditions.
     *
     * This function processes feature codes dynamically based on input parameters
     * such as service codes, LCDV, engine type, and Corvet attributes. It determines 
     * the appropriate feature configurations and appends them to the feature codes list.
     * Non FDS feature codes are also added here.
     *
     * @param array  $featureCodeConfig   The configuration array for feature codes loaded from YAML.
     * @param array  $serviceCodes        The list of available service codes from the Consumer rights API.
     * @param string $lcdv                The LCDV value of the vehicle.
     * @param string $engineType          The type of engine (ICE, PHEV, BEV, etc.).
     * @param array  $corvertAttributs    The list of Corvet attributes associated with the vehicle, from Corvet API.
     * @param mixed  $f2mcObj             An optional object related to Free2Move (F2MC) services from userData.
     * @param string $country             The country code for localization.   
     * @param bool   $includeNonFds       If false, only CVS service codes will be used to calculate feature codes. (Non FDS codes are not recalculated)
     *
     */
    public function dynamicStatusConfigCalculation(array $featureCodeConfig, array $serviceCodes, string $lcdv, string $engineType, array $corvertAttributs, $f2mcObj = null, $country = null, $includeNonFds = true)
    {
        try {
            $je_brand =  (!empty($lcdv) && substr($lcdv, 0, 4) === "1JJP") ? "JE" : null;

            $timestamp = time();
            $featureCodes = [];
            $bypass_service_keys = ['NAL01', 'NAL03']; //[EV Trip Planner / EV Routing]
            foreach ($serviceCodes as $serviceKey) {
                if (isset($featureCodeConfig[$serviceKey]) || in_array($serviceKey, ['NAL01', 'NAL03'], true) ){ // If the service exists in YAML data, fetch its details

                    switch ($serviceKey) {
                        case 'NAE01': //[Vehicle Health Report]
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                foreach ($featureCodeConfig[$serviceKey] as $feature) {
                                    if ($feature['code'] == 'VEHICLE_INFO') {
                                        $feature['config']['engine'] = VehicleTypeEntities::ENGINE_TYPES[$engineType] ?? 'UNKNOWN';
                                    }
                                    $featureCodes[] = $feature;
                                }
                            }
                            break;
                        case 'NAK01': //[Connected Navigation] Send2Nav
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                $feature['config']['protocol'] = FeatureCode::J4U_NAV_PROTOCOL;
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NAL01': //[Connected Navigation] EV Trip Planner / EV Routing
                            if ($engineType == FeatureCode::BATTERY_ELECTRIC_VEHICLE) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                $mapping = [
                                    'DRCNACD' => "1",
                                    'DRCNNCD' => "1",
                                    'DO906CD' => "1.1"
                                ];
                            
                                $matchedAttribute = array_intersect_key($mapping, array_flip($corvertAttributs));

                                if (!empty($matchedAttribute)) {
                                    $feature['config']['version'] = reset($matchedAttribute);
                                }
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NAM01': //[Digital Key]
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                if ($feature['code'] == 'DIGITAL_KEY') {
                                    $feature['config']['type'] = FeatureCode::J4U_DIGITAL_KEY_TYPE;
                                }
                                $featureCodes[] = $feature;
                            } else if ($je_brand == FeatureCode::BRAND_ALFA) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                if ($feature['code'] == 'DIGITAL_KEY') {
                                    $feature['config']['type'] = FeatureCode::A5U_DIGITAL_KEY;
                                }
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NAO01': ///[Remote LEV] Remontée de l'état de charge batterie - BEV
                        case 'NAO02': //[Remote LEV] Remontée de l'état de charge batterie - PHEV
                            $feature = reset($featureCodeConfig[$serviceKey]);
                            $featureCodes[] = $feature;
                            break;
                        case 'NAS01': //[Remote LEV] Contrôle préconditionnement thermique
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                $feature =$featureCodeConfig[$serviceKey]['CLIMATE_SCHEDULING'];
                                $featureCodes[] = $feature;
                            }

                            // check if $corvertAttributs has value DRE24CD
                            if (in_array('DRE24CD', $corvertAttributs, true)) {
                                foreach (['PRECONDIONNING_ON', 'PRECONDIONNING_OFF', 'AIR_CONDITIONNING_ON', 'AIR_CONDITIONNING_OFF'] as $key) {
                                    if (isset($featureCodeConfig[$serviceKey][$key])) {
                                        $featureCodes[] = $featureCodeConfig[$serviceKey][$key];
                                    }
                                }
                            }
                            break;
                        case 'NAS02': //[Remote LEV] Contrôle préconditionnement sans démarrage immédiat
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NAU01': //e-Routes
                            try {
                                $cdnUrl = $this->cdnUrl ?? null;

                                if (!$cdnUrl) {
                                    throw new \Exception(__CLASS__ . "::" . __METHOD__ . 'CDN URL is not configured.');
                                }
                        
                                $response = $this->client->request('GET', $cdnUrl);

                                if ($response->getStatusCode() === 200) {
                                    $data = $response->toArray();

                                    if (isset($data['eROUTES'])) { // Check if 'eROUTES' exists
                                        $feature = reset($featureCodeConfig[$serviceKey]);
                                        $feature['config']['linkAndroid'] = $data['eROUTES']['linkAndroid'] ?? '';
                                        $feature['config']['linkIos'] = $data['eROUTES']['linkIos'] ?? '';
                                        $featureCodes[] = $feature;
                                    } else {
                                        $this->logger->error(__CLASS__ . "::" . __METHOD__ . "eROUTES key is missing in CDN response.");
                                    }
                                } else {
                                    $this->logger->error(__CLASS__ . "::" . __METHOD__ . "Failed to fetch data from CDN. Status code: " . $response->getStatusCode());
                                }
                            } catch (\Exception $e) {
                                $this->logger->error(__CLASS__ . "::" . __METHOD__ . "Error in NAU01 case: " . $e->getMessage());
                            }
                            break;
                        case 'NAW01': //[Trips In Cloud]
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                foreach ($featureCodeConfig[$serviceKey] as $feature) {
                                    if ($feature['code'] == 'TRIPS') {
                                        $feature['config']['protocol'] = FeatureCode::J4U_TRIPS_PROTOCOL;
                                    } else if ($feature['code'] == 'VEHICLE_LOCATOR') {
                                        $feature['config']['location'] = FeatureCode::J4U_TRIPS_LOCATION;;
                                        $feature['config']['refresh'] = false;
                                    }
                                    $featureCodes[] = $feature;
                                }
                            }
                            break;
                        case 'NBM01': // [Remote LEV] Contrôle de la charge
                            foreach ($featureCodeConfig[$serviceKey] as $feature) {
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NBM02': //[Remote LEV] Contrôle de la charge avec scheduling
                            $region = $this->getRegionByCountry($country);
                            if ($region == FeatureCode::REGION_NAFTA) {
                                foreach ($featureCodeConfig[$serviceKey] as $feature) {
                                    $featureCodes[] = $feature;
                                }
                            }
                            break;
                        case 'NBI01': //[Remote LEV]
                            $region = $this->getRegionByCountry($country);
                            if ($region == FeatureCode::REGION_NAFTA) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NBG01': //[Remote LEV] Charge till 80%
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NCG01': //[Remote LEV] Conseil d'utilisation durabilité batterie
                            $feature = reset($featureCodeConfig[$serviceKey]);
                            $featureCodes[] = $feature;
                            break;
                        case 'NDF01':
                            
                            break;
                        case 'NEE02': //[Remote Access] Remote Locking (Lock+Unlock)
                            foreach ($featureCodeConfig[$serviceKey] as $feature) {
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NEF01': //[Remote Access] Remote Vehicle Tracking Aid (Horn+Light)
                            foreach ($featureCodeConfig[$serviceKey] as $feature) {
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NFC01': //[Connected Alarm] Alarm Status
                            $feature = reset($featureCodeConfig[$serviceKey]);
                            $featureCodes[] = $feature;
                            break;
                        case 'NFD01': //[Connected Alarm] Alarm Alert and History
                            if ($je_brand == FeatureCode::BRAND_JEEP) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                $featureCodes[] = $feature;
                            }
                            break;
                        case 'NAB01': //[UBI - Pay How You Drive]
                            $region = $this->getRegionByCountry($country);
                            if ($region == FeatureCode::REGION_NAFTA) {
                                $feature = reset($featureCodeConfig[$serviceKey]);
                                $featureCodes[] = $feature;
                            }
                            break;
                    }
                }
            }

            // Eligibility for SmartPhone Station Feature code
            $spsFeature = $this->spsEligibility($lcdv, $corvertAttributs);
            if ($spsFeature) {
                $featureCodes[] = $spsFeature;
            }

            // [Find My Car] Location: Destination of the last trip
            // All vehicles without NAW01
            // FeatureCode: VEHICLE_LOCATOR
            $findMyCarFeature = $this->getFindMyCarFeature($featureCodeConfig, $timestamp, $serviceCodes);
            if ($findMyCarFeature) {
                $featureCodes[] = $findMyCarFeature;
            }


            if ($includeNonFds) {
                // Non FDS codes, to be added duing add vehicle flow
                // [Gas Station Locator] FeatureCode: GAS_STATION_LOCATOR
                $gasStationFeature = $this->getGasStationLocatorFeature($featureCodeConfig, $timestamp, $engineType, $je_brand);
                if ($gasStationFeature) {
                    $featureCodes[] = $gasStationFeature;
                }

                // [Hydrogen Station Locator] FeatureCode: HYDROGEN_STATION_LOCATOR
                $hydrogenStationFeature = $this->getHydrogenStationLocatorFeature($featureCodeConfig, $timestamp, $engineType, $je_brand);
                if ($hydrogenStationFeature) {
                    $featureCodes[] = $hydrogenStationFeature;
                }

                // [Charging station locator] FeatureCode: CHARGE_STATION_LOCATOR
                $chargingStationFeature = $this->getChargingStationLocatorFeature($featureCodeConfig, $timestamp, $engineType, $lcdv);
                if ($chargingStationFeature) {
                    $featureCodes[] = $chargingStationFeature;
                }

                // [Charging station management] FeatureCode: CHARGING_STATION_MANAGEMENT
                $chargingStationManagementFeature = $this->getChargingStationManagementFeature($engineType, $f2mcObj, $country);
                if ($chargingStationManagementFeature) {
                    $featureCodes[] = $chargingStationManagementFeature;
                }

                // [Deep Refresh] FeatureCode: DEEP_REFRESH
                $deepRefreshFeature = $this->getDeepRefreshFeature($featureCodeConfig, $timestamp, $serviceCodes);
                if ($deepRefreshFeature) {
                    $featureCodes[] = $deepRefreshFeature;
                }
            }
             
            return $featureCodes;

        } catch (\Exception $e) {
            $this->logger->error("Error in " . __CLASS__ . "::" . __METHOD__ . $e->getMessage());
        }
    }

    function getRegionByCountry($countryCode = null): ?string
    {
        // Load market config from YAML
        $marketDataPath = __DIR__ . "/../../config/packages/market.yml";
        $data = Yaml::parseFile($marketDataPath);
        
        foreach ($data as $entry) {
            if (isset($entry['I_COUNTRY']) && $entry['I_COUNTRY'] === $countryCode) {
                return $entry['C_REGION'] ?? null;
            }
        }
        return null;
    }

    /**
     * Get the charging station management feature configuration
     *
     * @param string $engineType The engine type
     * @param mixed $f2mcObj Free2Move account data
     * @param string|null $country The country code
     * @return array | null The configured feature
     */
    public function getChargingStationManagementFeature(string $engineType, mixed $f2mcObj = null, ?string $country = null)
    {
        if (in_array($engineType, ['BEV', 'PHEV', 'HFCV'])) {
            $feature = FeatureCodeConfig::getAddVehicleFeatures('CHARGING_STATION_MANAGEMENT');
            $enrolmentStatus = FeatureCode::getEnrolmentStatus('NO_ACCOUNT_LINKING');

            if ($f2mcObj) {
                if (!$f2mcObj['isPaymentMethod']) {
                    $enrolmentStatus = FeatureCode::getEnrolmentStatus('NO_PAYMENT_METHOD');
                } elseif (!$f2mcObj['isAccountLinked']) {
                    $enrolmentStatus = FeatureCode::getEnrolmentStatus('NO_ACCOUNT_LINKING');
                } else {
                    $enrolmentStatus = FeatureCode::getEnrolmentStatus('COMPLETE');
                }
            }

            // If region of country is Nafta config.partner ==> "f2mc" else TBD
            $region = $country ? $this->getRegionByCountry($country) : FeatureCode::REGION_NAFTA;

            if ($region == FeatureCode::REGION_NAFTA) {
                $feature['config']['partner'] = FeatureCode::CSM_PARTNER_F2MC;
            } else {
                $feature['config']['partner'] = FeatureCode::CSM_PARTNER_TBD;
            }
            
            $feature['config']['enrolmentStatus'] = $enrolmentStatus;
            return $feature;
        }
        return null;
    }

    /**
     * Get the gas station locator feature configuration if applicable
     *
     * @param array $featureCodeConfig The feature code configuration
     * @param int $timestamp The calculation timestamp
     * @param string $engineType The engine type
     * @param string|null $brand The vehicle brand
     * @return array|null The configured feature or null if not applicable
     */
    private function getGasStationLocatorFeature(array $featureCodeConfig, int $timestamp, string $engineType, ?string $brand): ?array
    {
        if (in_array($engineType, ['ICE', 'PHEV', 'MHEV']) && $brand == FeatureCode::BRAND_JEEP) {
            $feature = FeatureCodeConfig::getAddVehicleFeatures('GAS_STATION_LOCATOR');
            return $feature;
        }
        
        return null;
    }

    /**
     * Get the hydrogen station locator feature configuration if applicable
     *
     * @param array $featureCodeConfig The feature code configuration
     * @param int $timestamp The calculation timestamp
     * @param string $engineType The engine type
     * @param string|null $brand The vehicle brand
     * @return array|null The configured feature or null if not applicable
     */
    private function getHydrogenStationLocatorFeature(array $featureCodeConfig, int $timestamp, string $engineType, ?string $brand): ?array
    {
        if (in_array($engineType, ['HFCV']) && $brand == FeatureCode::BRAND_JEEP) {
            $feature = FeatureCodeConfig::getAddVehicleFeatures('HYDROGEN_STATION_LOCATOR');
            return $feature;
        }
        
        return null;
    }

    /**
     * Get the charging station locator feature configuration if applicable
     *
     * @param array $featureCodeConfig The feature code configuration
     * @param int $timestamp The calculation timestamp
     * @param string $engineType The engine type
     * @param string|null $lcdv The LCDV value
     * @return array|null The configured feature or null if not applicable
     */
    private function getChargingStationLocatorFeature(array $featureCodeConfig, int $timestamp, string $engineType, ?string $lcdv): ?array
    {
        if (in_array($engineType, ['BEV', 'PHEV', 'HFCV'])) {
            $feature = FeatureCodeConfig::getAddVehicleFeatures('CHARGE_STATION_LOCATOR');
            $prefixes = ['1JJP', '1P', '1C', '1S', '1G'];
            
            if (isset($lcdv) && in_array(substr($lcdv, 0, 4), $prefixes)) {
                $config = [
                    'type' => 'partner',
                    'url' => 'free2move://',
                    'packageName' => 'com.f2m.esolutions.esolutions',
                    'appId' => '1608553487',
                    'appName' => 'eSolution'
                ];
            } else {
                $config = [
                    'type' => 'internal'
                ];
            }
            
            $feature['config'] = $config;
            return $feature;
        }
        
        return null;
    }

    /**
     * Get the deep refresh feature configuration if applicable
     *
     * @param array $featureCodeConfig The feature code configuration
     * @param int $timestamp The calculation timestamp
     * @param array $serviceCodes The service codes
     * @return array|null The configured feature or null if not applicable
     */
    private function getDeepRefreshFeature(array $featureCodeConfig, int $timestamp, array $serviceCodes): ?array
    {
        $matchingValues = ['NAW01', 'NEF01', 'NAO01', 'NAO02'];
        $matchedValues = array_intersect($matchingValues, $serviceCodes);

        if (!empty($matchedValues)) {
            $firstMatchedValue = reset($matchedValues); // Get the first encountered value
            $feature = $featureCodeConfig["VEHICLE_DEEP_REFRESH"];
            $feature['config']['value'] = $firstMatchedValue;
            return $feature;
        }
        
        return null;
    }

    /**
     * Get the Find My Car feature configuration if applicable
     *
     * @param array $featureCodeConfig The feature code configuration
     * @param int $timestamp The calculation timestamp
     * @param array $serviceCodes The service codes
     * @return array|null The configured feature or null if not applicable
     */
    private function getFindMyCarFeature(array $featureCodeConfig, int $timestamp, array $serviceCodes): ?array
    {
        // Add for all vehicles without NAW01
        $fds = "NAW01";
        if (!in_array($fds, $serviceCodes, true)) {
            $feature = reset($featureCodeConfig[FeatureCode::NON_NAW01]);
            return $feature;
        }
        
        return null;
    }

    private function getMockServiceCodes(): array
    {
        return [
            'NAE01',
            'NAK01',
            'NAL01',
            'NAM01',
            'NAO01',
            'NAO02',
            'NAS01',
            'NAS02',
            'NAU01',
            'NAW01', //check for NON_NAW01
            'NBM01',
            'NBM02',
            'NBI01',
            'NBG01',
            'NCG01',
            'NEE02',
            'NEF01',
            'NFC01',
            'NFD01',
            'VEHICLE_DEEP_REFRESH',
            'NAB01'
            //check for ADD_VEHICLE features
        ];
    }

    /**
     * Check if a vehicle is eligible for SmartPhone Station feature code and add it if eligible
     *
     * @param string $lcdv The LCDV value of the vehicle
     * @param array $corvertAttributs The list of Corvet attributes associated with the vehicle
     * @return array|null
     */
    private function spsEligibility(string $lcdv, array $corvertAttributs): ?array
    { 
        $feature = FeatureCodeConfig::getAddVehicleFeatures('SMARTPHONE_STATION');

        // Get SPS Eligibility rules form BO
        $spsEligibilityData = $this->getSPSEligibility();
        if (!$spsEligibilityData) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' No response for SPS Eligibility');
            return null;
        }

        $lcdvEligibility = $spsEligibilityData['lcdv'] ?? [];
        // $modelEligibility = $spsEligibilityData['model'] ?? [];
        if (empty($lcdv)) {
            return null;
        }

        $lcdv4 = substr($lcdv, 0, 4);

        // Find eligibility row for this LCDV
        $matchingEligibility = null;
        foreach ($lcdvEligibility as $eligibility) {
            if (!empty($eligibility['codes']) && in_array($lcdv4, $eligibility['codes'], true)) {
                $matchingEligibility = $eligibility;
                break;
            }
        }

        if (!$matchingEligibility) {
            return null;
        }

        $eligibilityRule = $matchingEligibility['eligibilityRule'] ?? null;
        $isEligibilityRuleFound = empty($eligibilityRule) || in_array($eligibilityRule, $corvertAttributs, true);

        if (!$isEligibilityRuleFound) {
            return null;
        }

        // Build filtered attributes map for quick lookup
        $filteredAttributes = [
            'DRC' => null,
            'DME' => null,
            'DLX' => null,
            'DZZ' => null
        ];
        foreach ($corvertAttributs as $value) {
            foreach ($filteredAttributes as $prefix => $_) {
                if (str_starts_with($value, $prefix)) {
                    $filteredAttributes[$prefix] = $value;
                }
            }
        }

        $vehicleCode = $lcdv4;

        if (!empty($filteredAttributes['DZZ'])) {
            $dzzValue = substr($filteredAttributes['DZZ'], 3, 2);

            if (in_array($dzzValue, ['0V', '01'], true)) {
                $vehicleCode = $lcdv4 . '_' . $dzzValue;
            }
        }
        $feature['config']['vehicleCode'] = $vehicleCode;
        $feature['config']['type'] = $matchingEligibility['type'] ?? 'spsGeneric';
        $feature['config']['eligibilityDisclamer'] = $matchingEligibility['eligibilityDisclaimer'] ?? '';

        return $feature;
    }

    /**
     * Retrieves SmartPhone Station (SPS) eligibility rules from the database.
     *
     * This method queries the MongoDB collection to fetch SPS eligibility configuration
     * data that determines which vehicles are eligible for SmartPhone Station features.
     * The data is structured with two scopes: LCDV and
     * MODEL, which contain the specific eligibility criteria for different vehicle types.
     *
     * @return array|null Returns an associative array containing SPS eligibility data
     *                    with the following structure:
     *                    [
     *                        'lcdv' => array,  // LCDV scope eligibility rules
     *                        'model' => array  // MODEL scope eligibility rules
     *                    ]
     *                    Returns null if the database query fails or no data is found.
     *
     */
    private function getSPSEligibility(): ?array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Fetching SPS eligibility data using ODM');

            // Get eligibility data grouped by scope (LCDV and MODEL)
            $eligibilityData = $this->spsEligibilityRepository->getSPSEligibilityData();

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' SPS eligibility data retrieved successfully', [
                'lcdv_count' => count($eligibilityData['lcdv'] ?? []),
                'model_count' => count($eligibilityData['model'] ?? [])
            ]);

            return $eligibilityData;

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Failed to fetch SPS eligibility data', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }
}
