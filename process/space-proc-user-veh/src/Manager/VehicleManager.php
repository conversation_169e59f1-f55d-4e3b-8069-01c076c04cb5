<?php

namespace App\Manager;


use App\Dto\AddVehicleInputDTO;
use App\Dto\AddVehicleOutputDTO;
use App\Dto\EditVehicleInputDTO;
use App\Event\SpaceVehicleUpdatedEvent;
use App\Helper\BrandHelper;
use App\Helper\BrandProvider;
use App\Helper\CultureHelper;
use App\Helper\ErrorResponse;
use App\Helper\RefreshVehicleHelper;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Helper\VehicleTypeEntities;
use App\Helper\WSResponse;
use App\Manager\Visual3DManager;
use App\Model\SystemVehicleData;
use App\Model\VehicleListV2\VehicleListResponseMapper;
use App\Model\VehicleModel;
use Space\MongoDocuments\Document\Vehicle;
use Space\MongoDocuments\Document\MileageData;
use App\MongoDB\UserData\UserDataDocument\VehicleMapper;
use App\MongoDB\UserData\UserDataDocument\Vehicle as LegacyVehicle;
use App\Service\CorvetService;
use App\Service\FeatureCodeService;

use App\Service\RefreshVehicleInterface;
use App\Service\SystemSdprClient;
use App\Service\SystemUserDataClient;
use App\Service\UserDataService;
use App\Service\VehicleLabelService;
use App\Service\VehicleService;
use App\Service\XFVehicleRefreshService;
use App\Service\XPVehicleRefreshService;
use App\Manager\SystemUserDBManager;
use App\MongoDB\UserData\UserDataDocument\UserDataDocument as LegacyUserDataDocument;
use App\Trait\LoggerTrait;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

use Throwable;
use Webmozart\Assert\Assert;
use App\Manager\CatalogManager;
use App\Mapper\EditVehicleMapper;
use App\Service\SystemUserDBService;
use stdClass;
use App\Model\FeatureCode;

/**
 * User vehicle Manager.
 */
class VehicleManager
{
    use LoggerTrait;

    private const ELIGIBILITY_NAVCOZAR = 'navcozar';
    private const ELIGIBILITY_REMOTELEV = 'remotelev';
    private const ELIGIBILITY_NAC = 'nac';

    public function __construct(
        private VehicleService $service,
        private SerializerInterface $serializer,
        private DenormalizerInterface $denormalizer,
        private ValidatorInterface $validator,
        private EventDispatcherInterface $dispatcher,
        private XPVehicleRefreshService $xPVehicleRefreshService,
        private XFVehicleRefreshService $xFVehicleRefreshService,
        private UserDataService $userDataService,
        private CorvetService $corvetService,
        private SystemUserDataClient $systemUserDataClient,
        private Visual3DManager $visual3DManager,
        private BrandHelper $brandHelper,
        private SystemSdprClient $systemSdprClient,
        private FeatureCodeService $featureCodeService,
        private VehicleLabelService $vehicleLabelService,
        private NormalizerInterface $normalizer,
        private CatalogManager $catalogManager,
        private SubscriptionManager $subscriptionManager,
        private SystemUserDBService $systemUserDBService,
        private SystemUserDBManager $systemUserDBManager
    ) {
    }

    /**
     * Get Vehicles On Order (migrated to ODM).
     */
    public function getVehicles(string $userId): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicles on order for user', [
                'userId' => $userId,
            ]);

            // Use ODM to get user data
            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' User not found in MongoDB ODM', [
                    'userId' => $userId,
                    'message' => 'No user document found - this could be normal if user has no data yet'
                ]);
                return new SuccessResponse(['vehicles' => []]);
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User found in MongoDB ODM', [
                'userId' => $userId,
                'userDataId' => $userData->getId(),
            ]);

            $vehicles = $userData->getVehicles();
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Retrieved vehicles from user document', [
                'userId' => $userId,
                'totalVehicles' => count($vehicles),
            ]);

            $vehicleModels = [];
            $vehicleIds = [];
            $onOrderCount = 0;
            $notOnOrderCount = 0;

            foreach ($vehicles as $index => $vehicle) {
                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Processing vehicle', [
                    'userId' => $userId,
                    'vehicleIndex' => $index,
                    'vin' => $vehicle->getVin(),
                    'brand' => $vehicle->getBrand(),
                    'model' => $vehicle->getModel(),
                    'status' => $vehicle->getStatus(),
                    'versionId' => $vehicle->getVersionId(),
                ]);

                // Filter for vehicles on order
                $isOrder = $this->isVehicleOnOrder($vehicle);

                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle order status check', [
                    'userId' => $userId,
                    'vin' => $vehicle->getVin(),
                    'isOrder' => $isOrder,
                    'status' => $vehicle->getStatus(),
                ]);

                if ($isOrder) {
                    $onOrderCount++;
                    $vehicleId = $vehicle->getDocumentId() ?? $vehicle->getVin(); // Using documentId as ID, fallback to VIN
                    $vehicleIds[] = $vehicleId;

                    // Convert ODM Vehicle to the expected API response format
                    $vehicleData = $this->convertODMVehicleToApiFormat($vehicle, $userId);
                    $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Converting vehicle to API format', [
                        'userId' => $userId,
                        'vin' => $vehicle->getVin(),
                        'vehicleData' => $vehicleData,
                    ]);

                    $vehicleModels[] = $vehicleData;
                } else {
                    $notOnOrderCount++;
                }
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle filtering completed', [
                'userId' => $userId,
                'totalVehicles' => count($vehicles),
                'onOrderVehicles' => $onOrderCount,
                'notOnOrderVehicles' => $notOnOrderCount,
                'finalVehicleModels' => count($vehicleModels),
            ]);

            // Mark orders as read (if this functionality is still needed)
            if (!empty($vehicleIds)) {
                $this->markOrdersAsRead($userId, $vehicleIds);
            }

            // Return the vehicles data directly in the expected format
            // The SuccessResponse will wrap this in a 'success' object, giving us {"success": {"vehicles": [...]}}
            return new SuccessResponse(['vehicles' => $vehicleModels]);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception VehicleManager::getVehicles ' . $e->getMessage(), [
                'userId' => $userId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Check if a vehicle is considered "on order"
     * Supports both legacy Vehicle and ODM Vehicle types
     */
    private function isVehicleOnOrder($vehicle): bool
    {
        // This logic needs to be defined based on your business requirements
        // For now, we'll be more inclusive to help with testing and debugging
        $status = $vehicle->getStatus();
        $vin = $vehicle->getVin();
        $brand = $vehicle->getBrand();
        $model = $vehicle->getModel();

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Checking vehicle order status', [
            'vin' => $vin,
            'status' => $status,
            'brand' => $brand,
            'model' => $model,
            'vehicleType' => get_class($vehicle),
        ]);

        // For testing purposes, let's include more statuses and also vehicles without status
        // You can adjust this logic based on your business requirements
        $onOrderStatuses = [
            'ORDERED',
            'PRODUCTION_START',
            'TO_BE_VALIDATED',
            'SSDP',
            'GSDP',
            'IN_PRODUCTION',
            'DELIVERED',
            'CREATION',
            'ORDER',
            'PENDING'
        ];

        // If no status is set, consider it as potentially on order for testing
        if (empty($status)) {
            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle has no status, considering as on order for testing');
            return true;
        }

        $isOnOrder = in_array($status, $onOrderStatuses);

        // Additional check for ODM vehicles: check feature codes for order indicators
        if (!$isOnOrder && $vehicle instanceof Vehicle) {
            $featureCodes = $vehicle->getFeatureCode() ?? [];
            foreach ($featureCodes as $featureCode) {
                if (isset($featureCode['code']) && str_contains($featureCode['code'], 'ORDER')) {
                    $isOnOrder = true;
                    break;
                }
            }
        }

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle order status result', [
            'vin' => $vin,
            'status' => $status,
            'isOnOrder' => $isOnOrder,
        ]);

        return $isOnOrder;
    }

    /**
     * Convert ODM Vehicle to the exact API response format expected by v1/vehicles endpoint
     */
    private function convertODMVehicleToApiFormat(Vehicle $vehicle, string $userId = ''): array
    {
        // Ensure all required fields have non-null values
        $vin = $vehicle->getVin() ?? '';
        $brand = $vehicle->getBrand() ?? 'UNKNOWN';
        $model = $vehicle->getModel() ?? '';
        $versionId = $vehicle->getVersionId() ?? '';
        $label = $vehicle->getLabel() ?? $model;

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Converting vehicle to API format', [
            'vin' => $vin,
            'brand' => $brand,
            'model' => $model,
            'versionId' => $versionId,
            'label' => $label,
            'userId' => $userId,
        ]);

        // Extract order information from the vehicle
        $orderInfo = $this->extractOrderInformationFromVehicle($vehicle, $userId);

        // Extract additional vehicle data from raw MongoDB document
        $additionalVehicleData = $this->extractAdditionalVehicleData($vehicle, $userId);

        // Build vehicleOrder object in the expected format
        $vehicleOrder = [
            'mopId' => $orderInfo['mopId'] ?? '',
            'orderFormId' => $orderInfo['orderFormId'] ?? '',
            'trackingStatus' => $orderInfo['trackingStatus'] ?? '',
            'isUpdated' => false, // Default value for backward compatibility
            'orderFormStatus' => $orderInfo['orderFormStatus'] ?? '',
        ];

        // Parse language and country from culture if available
        $language = null;
        $country = null;
        if (!empty($additionalVehicleData['culture'])) {
            $cultureParts = explode('-', $additionalVehicleData['culture']);
            if (count($cultureParts) >= 2) {
                $language = $cultureParts[0];
                $country = $cultureParts[1];
            }
        }

        // Use language from vehicle data if available, otherwise from culture
        $language = $additionalVehicleData['language'] ?? $language;

        // Use country from vehicle data if available, otherwise from culture
        $country = $additionalVehicleData['country'] ?? $country;

        // Use actual vehicle picture if available, otherwise fallback to brand default
        $visual = $additionalVehicleData['picture'] ?? BrandProvider::getBrandDefaultImage($brand);

        // Get the document ID for the vehicle
        $documentId = $vehicle->getDocumentId();

        // If no documentId is set, generate one and log a warning
        if (empty($documentId)) {
            $documentId = RefreshVehicleHelper::generateUid();
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Vehicle missing documentId, generated new one', [
                'vin' => $vin,
                'generatedDocumentId' => $documentId,
                'userId' => $userId,
            ]);
            // Note: We should save this back to the database, but for now just use it in response
        }

        // Return the exact format expected by the v1/vehicles API
        return [
            'id' => $documentId, // Using documentId as ID for consistency with v2 API
            'vin' => $vin,
            'label' => $label,
            'versionId' => $versionId,
            'brand' => $brand,
            'visual' => $visual,
            'language' => $language,
            'country' => $country,
            'isOrder' => $this->isVehicleOnOrder($vehicle),
            'vehicleOrder' => $vehicleOrder,
        ];
    }

    /**
     * Extract additional vehicle data from raw MongoDB document that's not available in ODM Vehicle
     */
    private function extractAdditionalVehicleData(Vehicle $vehicle, string $userId): array
    {
        $additionalData = [
            'country' => null,
            'culture' => null,
            'picture' => null,
            'language' => null,
        ];

        if (empty($userId)) {
            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' No userId provided, cannot extract additional vehicle data');
            return $additionalData;
        }

        try {
            // Get raw user data from MongoDB
            $rawUserData = $this->userDataService->getRawUserData($userId);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Raw user data structure analysis', [
                'userId' => $userId,
                'rawUserDataExists' => $rawUserData !== null,
                'rawUserDataKeys' => $rawUserData ? array_keys($rawUserData) : [],
                'hasVehicleField' => $rawUserData ? isset($rawUserData['vehicle']) : false,
                'hasVehiclesField' => $rawUserData ? isset($rawUserData['vehicles']) : false,
                'vehicleCount' => $rawUserData && isset($rawUserData['vehicle']) ? count($rawUserData['vehicle']) : 0,
                'vehiclesCount' => $rawUserData && isset($rawUserData['vehicles']) ? count($rawUserData['vehicles']) : 0,
            ]);

            if (!$rawUserData) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' No raw user data found at all', [
                    'userId' => $userId,
                    'vin' => $vehicle->getVin(),
                ]);
                return $additionalData;
            }

            // Check both 'vehicle' and 'vehicles' fields as the structure might vary
            $vehicleArray = null;
            if (isset($rawUserData['vehicle']) && is_array($rawUserData['vehicle'])) {
                $vehicleArray = $rawUserData['vehicle'];
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Using vehicle field', [
                    'userId' => $userId,
                    'vehicleCount' => count($vehicleArray),
                ]);
            } elseif (isset($rawUserData['vehicles']) && is_array($rawUserData['vehicles'])) {
                $vehicleArray = $rawUserData['vehicles'];
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Using vehicles field', [
                    'userId' => $userId,
                    'vehiclesCount' => count($vehicleArray),
                ]);
            }

            if (!$vehicleArray) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' No vehicle array found in raw data', [
                    'userId' => $userId,
                    'vin' => $vehicle->getVin(),
                    'availableFields' => $rawUserData ? array_keys($rawUserData) : [],
                ]);
                return $additionalData;
            }

            // Find the matching vehicle in raw data by VIN
            $targetVin = $vehicle->getVin();
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Searching for vehicle by VIN', [
                'userId' => $userId,
                'targetVin' => $targetVin,
                'totalVehicles' => count($vehicleArray),
            ]);

            foreach ($vehicleArray as $index => $rawVehicle) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Examining vehicle in detail', [
                    'userId' => $userId,
                    'index' => $index,
                    'vehicleVin' => $rawVehicle['vin'] ?? 'NO_VIN',
                    'targetVin' => $targetVin,
                    'vehicleKeys' => array_keys($rawVehicle),
                    'hasCountry' => isset($rawVehicle['country']),
                    'hasCulture' => isset($rawVehicle['culture']),
                    'hasPicture' => isset($rawVehicle['picture']),
                    'hasVisual' => isset($rawVehicle['visual']),
                    'hasImage' => isset($rawVehicle['image']),
                    'hasLanguage' => isset($rawVehicle['language']),
                    'country' => $rawVehicle['country'] ?? null,
                    'culture' => $rawVehicle['culture'] ?? null,
                    'picture' => $rawVehicle['picture'] ?? null,
                    'visual' => $rawVehicle['visual'] ?? null,
                    'image' => $rawVehicle['image'] ?? null,
                    'language' => $rawVehicle['language'] ?? null,
                    'brand' => $rawVehicle['brand'] ?? null,
                    'model' => $rawVehicle['model'] ?? null,
                    'versionId' => $rawVehicle['versionId'] ?? null,
                    'label' => $rawVehicle['label'] ?? null,
                ]);

                // Handle case where targetVin is null or empty - try to match by other criteria
                $isMatch = false;
                if (!empty($targetVin) && isset($rawVehicle['vin']) && $rawVehicle['vin'] === $targetVin) {
                    $isMatch = true;
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Matched vehicle by VIN', [
                        'userId' => $userId,
                        'targetVin' => $targetVin,
                        'rawVin' => $rawVehicle['vin'],
                    ]);
                } elseif (empty($targetVin) && $index === 0) {
                    // If VIN is null/empty, try to match the first vehicle as a fallback
                    $isMatch = true;
                    $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' VIN is null/empty, using first vehicle as fallback', [
                        'userId' => $userId,
                        'targetVin' => $targetVin,
                        'vehicleIndex' => $index,
                        'rawVin' => $rawVehicle['vin'] ?? 'NO_VIN',
                    ]);
                }

                if ($isMatch) {
                    $additionalData['country'] = $rawVehicle['country'] ?? null;
                    $additionalData['culture'] = $rawVehicle['culture'] ?? null;
                    $additionalData['language'] = $rawVehicle['language'] ?? null;
                    // Check multiple possible fields for visual/picture data
                    $additionalData['picture'] = $rawVehicle['picture'] ?? $rawVehicle['visual'] ?? $rawVehicle['image'] ?? null;

                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found matching vehicle with additional data', [
                        'userId' => $userId,
                        'vin' => $targetVin,
                        'matchedBy' => !empty($targetVin) ? 'VIN' : 'fallback_first_vehicle',
                        'country' => $additionalData['country'],
                        'culture' => $additionalData['culture'],
                        'language' => $additionalData['language'],
                        'picture' => $additionalData['picture'],
                        'rawVehicleKeys' => array_keys($rawVehicle),
                        'rawVehicleData' => $rawVehicle, // Log the entire raw vehicle for debugging
                    ]);
                    break;
                }
            }

            if ($additionalData['country'] === null && $additionalData['culture'] === null && $additionalData['picture'] === null && $additionalData['language'] === null) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' No matching vehicle found or no additional data available', [
                    'userId' => $userId,
                    'targetVin' => $targetVin,
                    'totalVehiclesChecked' => count($vehicleArray),
                ]);
            }

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error extracting additional vehicle data', [
                'userId' => $userId,
                'vin' => $vehicle->getVin(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $additionalData;
    }

    /**
     * Convert ODM Vehicle to array format for backward compatibility
     */
    private function convertODMVehicleToArray(Vehicle $vehicle, string $userId = ''): array
    {
        // Ensure all required fields have non-null values for VehicleModel compatibility
        $vin = $vehicle->getVin() ?? '';
        $brand = $vehicle->getBrand() ?? 'UNKNOWN';
        $model = $vehicle->getModel() ?? '';
        $versionId = $vehicle->getVersionId() ?? '';

        $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Converting vehicle with null handling', [
            'originalVin' => $vehicle->getVin(),
            'originalBrand' => $vehicle->getBrand(),
            'originalModel' => $vehicle->getModel(),
            'originalVersionId' => $vehicle->getVersionId(),
            'processedVin' => $vin,
            'processedBrand' => $brand,
            'processedModel' => $model,
            'processedVersionId' => $versionId,
        ]);

        // Extract order information from the vehicle
        $orderInfo = $this->extractOrderInformationFromVehicle($vehicle, $userId);

        // Extract additional vehicle data from raw MongoDB document
        $additionalVehicleData = $this->extractAdditionalVehicleData($vehicle, $userId);

        // Parse language and country from culture if available
        $language = null;
        $country = null;
        if (!empty($additionalVehicleData['culture'])) {
            $cultureParts = explode('-', $additionalVehicleData['culture']);
            if (count($cultureParts) >= 2) {
                $language = $cultureParts[0];
                $country = $cultureParts[1];
            }
        }

        // Use language from vehicle data if available, otherwise from culture
        $language = $additionalVehicleData['language'] ?? $language;

        // Use country from vehicle data if available, otherwise from culture
        $country = $additionalVehicleData['country'] ?? $country;

        // Use actual vehicle picture if available, otherwise fallback to brand default
        $visual = $additionalVehicleData['picture'] ?? BrandProvider::getBrandDefaultImage($brand);

        // Get the document ID for the vehicle
        $documentId = $vehicle->getDocumentId();

        // If no documentId is set, generate one and log a warning
        if (empty($documentId)) {
            $documentId = RefreshVehicleHelper::generateUid();
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Vehicle missing documentId, generated new one', [
                'vin' => $vin,
                'generatedDocumentId' => $documentId,
                'userId' => $userId,
            ]);
        }

        return [
            'id' => $documentId, // Using documentId as ID for consistency
            '_id' => $documentId,
            'vin' => $vin,
            'brand' => $brand, // Ensure not null
            'model' => $model, // Ensure not null
            'label' => $vehicle->getLabel() ?? $model, // Use label or fallback to model
            'versionId' => $versionId, // Ensure not null
            'visual' => $visual,
            'language' => $language,
            'country' => $country,
            'isOrder' => $this->isVehicleOnOrder($vehicle),
            'vehicleOrder' => [
                'mopId' => $orderInfo['mopId'] ?? '',
                'orderFormId' => $orderInfo['orderFormId'] ?? '',
                'trackingStatus' => $orderInfo['trackingStatus'] ?? '',
                'isUpdated' => false, // Default value for backward compatibility
                'orderFormStatus' => $orderInfo['orderFormStatus'] ?? '',
            ],
            // Additional fields for VehicleModel compatibility
            'registrationNumber' => $vehicle->getRegistrationNumber() ?? '',
            'color' => $vehicle->getColor() ?? '',
            'energy' => $vehicle->getEnergy() ?? '',
            'status' => $vehicle->getStatus() ?? '',
            'featureCode' => $vehicle->getFeatureCode() ?? [],
        ];
    }

    /**
     * Set isUpdated to  false so the front knows the new orders from already seens ones.
     * Migrated to use ODM implementation.
     *
     * @return void
     */
    public function markOrdersAsRead(string $userId, array $vehicleIds)
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Marking orders as read using ODM (optimized)', [
                'userId' => $userId,
                'vehicleCount' => count($vehicleIds),
            ]);

            // Since the ODM model doesn't have the 'isUpdated' field and the setOrderIsUpdated method
            // just logs that the field is not in the ODM model, we can optimize this by doing a single
            // batch operation or skip it entirely if not needed.

            // For now, we'll use a single bulk operation through UserDataService
            $success = $this->userDataService->markMultipleOrdersAsRead($userId, $vehicleIds);

            if (!$success) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Failed to mark orders as read in bulk', [
                    'userId' => $userId,
                    'vehicleCount' => count($vehicleIds),
                ]);
            } else {
                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Successfully marked orders as read in bulk', [
                    'userId' => $userId,
                    'vehicleCount' => count($vehicleIds),
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' Could not mark orders as read', [
                'userId' => $userId,
                'vehicleCount' => count($vehicleIds),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * create or update vehicle data function.
     */
    public function createOrUpdateVehicle(array $content): ResponseArrayFormat
    {
        try {
            $userId = $content['userId'] ?? null;
            $vehicleModel = $this->getVehicleModel($content);
            $errorsMessages = $this->vehicleValuesValidate($vehicleModel);
            if (empty($userId)) {
                $errorsMessages[] = 'The user ID value should not be blank.';
            }
            if ($errorsMessages) {
                return new ErrorResponse($errorsMessages);
            }

            $response = $this->service->createOrUpdateVehicle($userId, $vehicleModel);
            if (Response::HTTP_OK == $response->getCode() || Response::HTTP_CREATED == $response->getCode()) {
                $data = [
                    'message' => 'Vehicle data has been saved successfully',
                    'mopId' => $content['mopId'],
                ];

                $spaceVehicleUpdatedEvent = new SpaceVehicleUpdatedEvent(
                    $userId,
                    $vehicleModel,
                    $content
                );
                $this->dispatcher->dispatch(
                    $spaceVehicleUpdatedEvent,
                    SpaceVehicleUpdatedEvent::class
                );
                if ($spaceVehicleUpdatedEvent->getResponse() instanceof ErrorResponse) {
                    $this->logger->error(
                        'An error has occurred while sync MyMarque vehicle data with Space',
                        [
                            'userId' => $userId,
                            'model' => $vehicleModel,
                            'error' => $spaceVehicleUpdatedEvent->getResponse()->toArray(),
                        ]
                    );
                }
                if ($spaceVehicleUpdatedEvent->getResponse() instanceof SuccessResponse) {
                    // adding logic to update the flag isUpdated to true for the vehicle
                }

                return new SuccessResponse($data);
            }
            $data = [
                'raison' => 'An error has occurred, vehicle data has not been saved',
                'mopId' => $content['mopId'],
            ];

            return new ErrorResponse($data);
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * check input errors.
     */
    public function vehicleValuesValidate(VehicleModel $vehicleModel): array
    {
        $errors = $this->validator->validate($vehicleModel);
        $messages = [];
        if (count($errors) > 0) {
            foreach ($errors as $error) {
                $messages[] = $error->getMessage();
            }
        }
        $errors = $this->validator->validate($vehicleModel->getVehicleOrder());
        if (count($errors) > 0) {
            foreach ($errors as $error) {
                $messages[] = $error->getMessage();
            }
        }

        return $messages;
    }

    /**
     * mapping content like a array data.
     */
    public function getVehicleModel(array $content): VehicleModel
    {
        $culture = CultureHelper::parseCulture($content['culture']);
        $content['country'] = $culture['country'];
        $content['language'] = $culture['language'];
        $content = $this->manageOVData($content);

        if (empty($content['visual'])) {
            $content['visual'] = BrandProvider::getBrandDefaultImage($content['brand']);
        }

        $content['vehicleOrder'] = [
            'mopId' => $content['mopId'] ?? '',
            'orderFormId' => strval($content['orderFormId'] ?? ''),
            'trackingStatus' => $content['trackingStatus'] ?? '',
            'orderFormStatus' => $content['orderFormStatus'] ?? '',
        ];

        return $this->serializer->deserialize(json_encode($content), VehicleModel::class, 'json');
    }

    /**
     * get vehicle data with GB cases.
     */
    public function manageOVData(array $vehicle): array
    {
        if ($this->service->isOVVehicle($vehicle['versionId'])) {
            $allAttributes = $this->service->getVinAttributes($vehicle['vin'], 'OP'); /* forcing OP brand */
            $brand = $vehicle['brand'] = $this->service->getVehicleBrand($allAttributes, $vehicle['country']);
            if ('VX' == $brand) {
                $vehicle['language'] = 'en';
                $vehicle['country'] = 'GB';
            }
        }

        return $vehicle;
    }

    /**
     * Get Vehicles Summary.
     */
    public function getVehicleSummary(string $vehicleId, string $userId): ResponseArrayFormat
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle summary', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
            ]);

            $vehicle = $this->getVehicleInfo($vehicleId, $userId);
            $vehicleOrder = $vehicle->getVehicleOrder();
            $brand = $vehicle->getBrand();
            $country = $vehicle->getCountry();
            if (empty($country)) {
                $country = 'AT';
            }
            $orderFormId = $vehicleOrder->getOrderFormId();
            $mopId = $vehicleOrder->getMopId();

            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Vehicle order information', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
                'brand' => $brand,
                'country' => $country,
                'orderFormId' => $orderFormId,
                'mopId' => $mopId,
            ]);

            // Check if we have valid order information
            if (empty($orderFormId) || empty($mopId)) {
                $this->logger->warning(__CLASS__ . '::' . __METHOD__ . ' No order information available for vehicle', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                    'orderFormId' => $orderFormId,
                    'mopId' => $mopId,
                ]);
                return new ErrorResponse('No order information available for this vehicle', Response::HTTP_NOT_FOUND);
            }

            $response = $this->service->getOrderSummary($orderFormId, $mopId, $brand, $country);
            if (Response::HTTP_OK == $response->getCode()) {
                return new SuccessResponse(['url' => $response->getData()['success']['pdf_url'] ?? '']);
            }
            $errorMessage = $response->getData()['error']['message'] ?? 'server error';
            $this->logger->error(__METHOD__ . ' Error response ' . $errorMessage);

            return new ErrorResponse($errorMessage, $response->getCode());
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception ' . $e->getMessage());

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function getVehicleInfo(string $vehicleId, string $userId): VehicleModel
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle info', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
            ]);

            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                throw new \Exception('User not found', Response::HTTP_NOT_FOUND);
            }

            $vehicles = $userData->getVehicles();
            $vehicle = null;

            foreach ($vehicles as $v) {
                // Check multiple possible identifiers for the vehicle
                // 1. Check VIN (primary identifier)
                // 2. Check document ID (MongoDB 'id' field)
                // 3. Check if vehicleId matches any order-related identifiers from raw data

                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Checking vehicle match', [
                    'vehicleId' => $vehicleId,
                    'vehicleVin' => $v->getVin(),
                    'vehicleDocumentId' => $v->getDocumentId(),
                ]);

                if ($v->getVin() === $vehicleId ||
                    $v->getDocumentId() === $vehicleId) {
                    $vehicle = $v;
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found vehicle by direct match', [
                        'vehicleId' => $vehicleId,
                        'matchType' => $v->getVin() === $vehicleId ? 'VIN' : 'documentId',
                    ]);
                    break;
                }
            }

            // If no direct match found, try to find by order data (mopId, orderFormId)
            // Use a more efficient approach by getting raw data once and checking all vehicles
            if (!$vehicle) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No direct match found, checking order data', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                ]);

                // Get raw user data once to avoid multiple MongoDB calls
                $rawUserData = $this->userDataService->getRawUserData($userId);
                if ($rawUserData && isset($rawUserData['vehicle'])) {
                    foreach ($rawUserData['vehicle'] as $index => $vehicleData) {
                        $vehicleOrder = $vehicleData['vehicleOrder'] ?? null;
                        if ($vehicleOrder &&
                            (($vehicleOrder['mopId'] ?? '') === $vehicleId ||
                             ($vehicleOrder['orderFormId'] ?? '') === $vehicleId)) {

                            // Find the corresponding ODM vehicle by index or VIN
                            $matchedVehicle = null;
                            $vehicleVin = $vehicleData['vin'] ?? null;

                            if ($vehicleVin) {
                                // Try to find by VIN first
                                foreach ($vehicles as $v) {
                                    if ($v->getVin() === $vehicleVin) {
                                        $matchedVehicle = $v;
                                        break;
                                    }
                                }
                            }

                            // If still not found, try by index (less reliable but fallback)
                            if (!$matchedVehicle && $index < count($vehicles)) {
                                $matchedVehicle = $vehicles[$index];
                            }

                            if ($matchedVehicle) {
                                $vehicle = $matchedVehicle;
                                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found vehicle by order data match', [
                                    'vehicleId' => $vehicleId,
                                    'matchType' => ($vehicleOrder['mopId'] ?? '') === $vehicleId ? 'mopId' : 'orderFormId',
                                    'mopId' => $vehicleOrder['mopId'] ?? '',
                                    'orderFormId' => $vehicleOrder['orderFormId'] ?? '',
                                    'vehicleVin' => $vehicleVin,
                                ]);
                                break;
                            }
                        }
                    }
                }
            }

            if (!$vehicle) {
                throw new \Exception('Vehicle not found', Response::HTTP_NOT_FOUND);
            }

            // Convert ODM Vehicle to legacy VehicleModel format for backward compatibility
            // Handle null values properly to prevent denormalization errors

            // Try to get order information from the ODM vehicle or legacy data
            $mopId = '';
            $orderFormId = '';
            $trackingStatus = '';
            $orderFormStatus = '';

            // Check if order information exists in the raw MongoDB document
            // The legacy system stored order data in vehicleOrder field
            $orderInfo = $this->extractOrderInformationFromVehicle($vehicle, $userId);
            if ($orderInfo) {
                $mopId = $orderInfo['mopId'] ?? '';
                $orderFormId = $orderInfo['orderFormId'] ?? '';
                $trackingStatus = $orderInfo['trackingStatus'] ?? '';
                $orderFormStatus = $orderInfo['orderFormStatus'] ?? '';

                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found order information for vehicle', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                    'mopId' => $mopId,
                    'orderFormId' => $orderFormId,
                    'trackingStatus' => $trackingStatus,
                    'orderFormStatus' => $orderFormStatus,
                ]);
            } else {
                $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' No order information found for vehicle', [
                    'vehicleId' => $vehicleId,
                    'userId' => $userId,
                ]);
            }

            $vehicleArray = [
                'id' => $vehicle->getVin() ?? '', // Using VIN as ID
                'vin' => $vehicle->getVin() ?? '',
                'brand' => $vehicle->getBrand() ?? 'UNKNOWN', // Handle null brand
                'model' => $vehicle->getModel() ?? '', // Handle null model
                'versionId' => $vehicle->getVersionId() ?? '', // Handle null versionId
                'country' => '', // Will need to be set from user data or context
                'vehicleOrder' => [
                    'mopId' => $mopId,
                    'orderFormId' => $orderFormId,
                    'trackingStatus' => $trackingStatus,
                    'orderFormStatus' => $orderFormStatus,
                ]
            ];

            $this->logger->debug(__CLASS__ . '::' . __METHOD__ . ' Converting ODM vehicle to VehicleModel', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
                'vehicleArray' => $vehicleArray,
            ]);

            return $this->denormalizer->denormalize($vehicleArray, VehicleModel::class);

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting vehicle info', [
                'vehicleId' => $vehicleId,
                'userId' => $userId,
                'exception' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    public function getUserVehiclesData(
        string $userId,
        string $brand,
        string $language,
        string $country
    ): ResponseArrayFormat {
        try {
            $filters = [
                'brand' => $brand,
                'country' => $country
            ];

            // retrieve user data from MongoDB ODM, if exists
            $odmUserData = $this->userDataService->findUserById($userId);
            $this->logger->debug(__METHOD__ . ' Initial user vehicles data retrieved', [
                'userId' => $userId,
                'userDataDocument' => $odmUserData ? get_class($odmUserData) : 'null'
            ]);

            // Convert ODM UserData to legacy UserDataDocument for compatibility with refresh services
            $userDataDocument = $this->convertODMToLegacyUserDataDocument($odmUserData, $userId);

            // retrieve vehicles data for xF brands
            $xfVehicles = $this->xFVehicleRefreshService->retrieveVehicles($userDataDocument, $brand, $language, $country);

            // retrieve vehicles data for xP brands
            $xpVehicles = $this->xPVehicleRefreshService->retrieveVehicles($userDataDocument, $brand, $language, $country);

            $this->logger->debug(__METHOD__ . ' User vehicles data refreshed data retried', [
                'xfVehicles' => $xfVehicles,
                'xpVehicles' => $xpVehicles,
            ]);

            // Update all vehicles
            $stellantisVehicles = array_merge($xpVehicles, $xfVehicles);
            foreach ($stellantisVehicles as $vehicle) {
                Assert::isInstanceOf($vehicle, SystemVehicleData::class);

                // update vehicles data on MongoDB for the user
                $this->saveUserDataDocumentVehicles($userId, $vehicle->getVin(), $vehicle, $country);
            }

            // for GSDP sdp remove all vehicles that are not in the list        
            $this->userDataService->removeUserGSPDVehicles($userId, $brand, array_keys($xfVehicles));

            // read a fresh copy of vehicles data from MongoDB
            $userVehicles = $this->userDataService->getVehicleByUserIdAndBrand($userId, $brand);
            $this->logger->debug(__METHOD__ . ' User vehicles data refreshed successfully', [
                'userId' => $userId,
                'userDataDocument' => $userDataDocument
            ]);

            $vehiclesList = [];
            foreach ($userVehicles as $userVehicle) {
                Assert::isInstanceOf($userVehicle, Vehicle::class);

                // Convert ODM Vehicle to legacy Vehicle format for the mapper
                $legacyVehicle = $this->convertODMVehicleToLegacy($userVehicle);
                $vehicleListResponse = VehicleListResponseMapper::mapUserDataDocumentVehicle($legacyVehicle);
                $vehiclesList[] = $this->normalizer->normalize($vehicleListResponse, null, ['groups' => ['xp_vehicle_list']]);
            }

            return new SuccessResponse($vehiclesList, Response::HTTP_OK);

        } catch (Throwable $e) {
            $this->logger->error(__METHOD__ . ': Catched Exception', [
                'userId' => $userId,
                'brand' => $brand,
                'country' => $country,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return new ErrorResponse(
                'An error occurred while fetching user vehicles. ' . $e->getMessage(),
                Response::HTTP_BAD_REQUEST
            );
        }
    }

    /**
     * insert or update vehicle data in user document.
     *
     * @return void
     */
    private function saveUserDataDocumentVehicles(string $userId, string $vin, SystemVehicleData $discoveredVehicle, ?string $country = null): void
    {
        $userVehicle = $this->userDataService->findUserVehicleByVin($userId, $vin);
        if (null === $userVehicle) {
            $this->logger->debug(__METHOD__ . ' User vehicle not found', [
                'userId' => $userId,
                'vin' => $vin
            ]);
            // add the vehicle if it doesn't exist
            $userVehicle = VehicleMapper::mapSystemVehicleData($discoveredVehicle);
            $userVehicle->id = RefreshVehicleHelper::generateUid();

            // Adding CSM feature code here
            $featureCode = $this->featureCodeService->getChargingStationManagementFeature($userVehicle->type, null, $country);
            if ($featureCode !== null) {
                if (!isset($userVehicle->featureCodes)) {
                    $userVehicle->featureCodes = [];
                }
                $userVehicle->featureCodes[] = $featureCode;
            }

            $this->userDataService->addVehicleToUserDocument($userId, $userVehicle);
            $this->logger->debug(__METHOD__ . ' User vehicle added', [
                'userId' => $userId,
                'vin' => $vin
            ]);
        } else {
            // update the vehicle if it exists
            $updatedUserVehicle = VehicleMapper::mapSystemVehicleData($discoveredVehicle, $userVehicle);
            $this->logger->debug(__METHOD__ . ' Updating user vehicle data', [
                'discoveredVehicle' => $discoveredVehicle,
                'userVehicle' => $userVehicle,
                'updatedUserVehicle' => $updatedUserVehicle
            ]);

            $this->userDataService->updateVehicleInUserDocument($userId, $updatedUserVehicle);
            $this->logger->debug(__METHOD__ . ' User vehicle updated', [
                'userId' => $userId,
                'vin' => $vin
            ]);
        }
    }

    public function addSSDPVehicle(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        try {
            if (!$this->brandHelper->isSsdpBrand($vehicleDto->getBrand())) {
                $this->logger->error(__METHOD__ . ' : Error brand not XP');
                return new ErrorResponse($vehicleDto->getBrand() . ' Brand not supported', Response::HTTP_BAD_REQUEST);
            }
            $vehicle = $this->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
            if ($vehicle) {
                $this->logger->error(__METHOD__ . ' : Error vehicle already exist');
                return new ErrorResponse('Vehicle already exist', Response::HTTP_BAD_REQUEST);
            }

            $userDbId = $this->getUserDbId($vehicleDto->getUserId());
            if (!$userDbId) {
                $this->logger->error(__METHOD__ . ' : Error userDbId not found');
                return new ErrorResponse('User DB ID not found', Response::HTTP_NOT_FOUND);
            }
            $corvetData = $this->getCorvetData($vehicleDto->getVin(), $vehicleDto->getBrand());
            $this->logger->debug(__METHOD__ . ' Corvet data', [
                'corvetData' => $corvetData
            ]);
            $lcdv = $this->getLcdv($corvetData);
            if (!$lcdv) {
                $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
            }
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
            $vehicleSettings = $this->findVehicleLabelByLcdv($lcdv);
            $this->logger->debug(__METHOD__ . ' Vehicle settings', [
                'vehicleSettings' => $vehicleSettings
            ]);
            $label = $vehicleSettings['label'] ?? null;
            $isO2x = $vehicleSettings['isO2x'] ?? false;
            $visualUrl = $vehicleSettings['defaultImage'] ?? null;
            $sdp = $vehicleSettings['sdp'] ?? '';
            if (!$label) {
                $this->logger->error(__METHOD__ . ' : Error label not found while calling mongoDb vehicleLabel');
                return new ErrorResponse('Label Not found ', Response::HTTP_NOT_FOUND);
            }
            $params = [
                'vin' => $vehicleDto->getVin(),
                'brand' => $vehicleDto->getBrand(),
                'country' => $vehicleDto->getCountry(),
                'userId' => $vehicleDto->getUserId(),
                'commercialName' => $label,
                'pictureUrl' => $visualUrl
            ];
            $params = array_filter($params, function ($value) {
                return $value !== null;
            });

            $addVehicleInUserDbResponse = $this->systemUserDBManager->addVehicle($userDbId, $params);
            $this->logger->info(__METHOD__ . " => Response from add vehicle in user db", ['code' => $addVehicleInUserDbResponse->getCode(), 'data' => $addVehicleInUserDbResponse->getData()]);
            if ($addVehicleInUserDbResponse->getCode() !== Response::HTTP_OK) {
                $this->logger->error(__METHOD__ . ' : Error adding vehicle in user db');
                $data = $addVehicleInUserDbResponse->getData();
                $message = $data['error']['message'] ?? $data;
                $errorResponse = new ErrorResponse($message, $addVehicleInUserDbResponse->getCode());
                $errorResponse->setErrors(($data['error']['errors'] ?? []));
                return $errorResponse;
            }

            // Extract picture URL from system user DB response
            $userDbResponseData = $addVehicleInUserDbResponse->getData();
            if (isset($userDbResponseData['success']['pictureUrl'])) {
                $visualUrl = $userDbResponseData['success']['pictureUrl'];
                $this->logger->debug(__METHOD__ . ' Using picture URL from system user DB', [
                    'pictureUrl' => $visualUrl
                ]);
            }
            $vehicleOutputDto = new AddVehicleOutputDTO();
            $corvertAttributs = $this->getManagedAttributes($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
            $vehiclePlugTypeNumber = VehicleTypeEntities::getType('D7Q', $corvertAttributs);
            $vehiclePlugType = VehicleTypeEntities::PLUG_TYPES[$vehiclePlugTypeNumber] ?? '';
            $vehicleOutputDto->setAddStatus('COMPLETE');
            $vehicleOutputDto->setSdp($sdp);
            $vehicleOutputDto->setWarrantyStartDate($vehicleData['VEH_WARRANTY_START_DATE'] ?? null);
            $year = $vehicleData['VEH_YEAR'] ?? $vehicleData['YEAR'] ?? $vehicleData['ANNEE_MODELE'] ?? null;
            $vehicleOutputDto->setYear($year);
            $this->logger->debug(__METHOD__ . ' Setting year from Corvet data', [
                'year' => $year,
                'vehicleData' => $vehicleData
            ]);
            $vehicleOutputDto->setRegTimestamp(time());
            $vehicleOutputDto->setType($vehicleType);
            $vehicleOutputDto->setPlugType($vehiclePlugType);
            $vehicleOutputDto->setConnectorType($vehiclePlugType);
            $vehicleOutputDto->setLabel($label);
            $vehicleOutputDto->setLcdv($lcdv);
            $vehicleOutputDto->setVisual($visualUrl);
            $vehicleOutputDto->setFeaturesCode($this->featureCodeService->getFeaturesCode($vehicleDto->getUserId(), $vehicleDto->getVin(), $lcdv, $vehicleType, $corvertAttributs, null, null, true, $userDbId));
            $vehicleOutputDto->setFeatureCodeExpiryTo24HoursFromNow();
            $vehicleOutputDto->setIsOrder(false);
            $vehicleOutputDto->setMake($vehicleDto->getBrand());
            $vehicleOutputDto->setMarket($vehicleDto->getCountry());
            $vehicleOutputDto->setCulture(CultureHelper::createCulture($vehicleDto->getLanguage(), $vehicleDto->getCountry()));
            $vehicleOutputDto->setIsO2X($isO2x);
            $vehicleOutputDto->setLastUpdate(time());
            // calling mongoDb to push new vehicle
            $mongodbUpdateResponse = $this->updateVehicleData($vehicleDto, $vehicleOutputDto);
            if ($mongodbUpdateResponse->getCode() == Response::HTTP_OK) {
                $this->logger->info(__METHOD__ . ' : vehicle added successfully in the mongoDb');
                return new SuccessResponse("Vehicle added Successfully", Response::HTTP_CREATED);
            }
            $this->logger->error(__METHOD__ . " : Failed to add vehicle. Code: " . $mongodbUpdateResponse->getCode() . ". Message: " . $mongodbUpdateResponse->getData()['error']['message'] ?? $mongodbUpdateResponse->getData());
            return new ErrorResponse($mongodbUpdateResponse->getData(), $mongodbUpdateResponse->getCode());
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . " : Error adding vehicle in user db", ['vin' => $vehicleDto->getVin(), 'exception' => $e->getMessage()]);
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }


    public function mockCorvetData(): array
    {
        $array = [
            "ENTETE" => [
                "EMETTEUR" => "MYM_PREPROD"
            ],
            "VEHICULE" => [
                "@attributes" => [
                    "Existe" => "O"
                ],
                "DONNEES_VEHICULE" => [
                    "WMI" => "VYE",
                    "VDS" => "ATTEN0",
                    "VIS" => "SPU00024",
                    "VIN" => "ZARHBTTG3R9011445",
                    "LCDV_BASE" => "1JJPSYNZDFL0A0A0M0ZZGHFY",
                    "N_APV_PR" => [],
                    "ANNEE_MODELE" => "0A",
                    "MARQUE_COMMERCIALE" => "0J",
                    "DATE_DEBUT_GARANTIE" => [],
                    "DATE_ENTREE_COMMERCIALISATION" => "18/12/2024 17:23:00",
                    "LIGNE_DE_PRODUIT" => "JP"
                ],
                "LISTE_ATTRIBUTES_7" => [
                    "ATTRIBUT" => [
                        "D0000CD", "D0103CD", "D0202CD", "D0301CD", "D0400CD", "D0500CD", "D0600CD", "D0701CD", "D0800CD", "D0900CD", "D1000CD", 
                        "D1100CD", "D1202CD", "D1301CD", "D1500CD", "D1701CD", "D1801CD", "D1901CD", "D1C03CD", "D1D05CD", "D1E55CD", "D1F24CD", 
                        "D1G30CD", "D1H09CD", "D2501CD", "D2802CD", "D2900CD", "D2A00CD", "D3201CD", "D4100CD", "D4401CD", "D4I00CD", "D4K00CD", 
                        "D5000CD", "D5102CD", "D5901CD", "D5M04CD", "D5N08CD", "D5O01CD", "D6007CD", "D6100CD", "D6200CD", "D6302CD", "D6404CD", 
                        "D6508CD", "D6602CD", "D6706CD", "D6803CD", "D6D03CD", "D6E01CD", "D6F02CD", "D6G03CD", "D6H05CD", "D6J00CD", "D6K04CD", 
                        "D6L04CD", "D6N01CD", "D6O00CD", "D6Q01CD", "D6V07CD", "D6W02CD", "D6X03CD", "D7003CD", "D7A01CD", "D7B02CD", "D7C02CD", 
                        "D7E00CD", "D7H01CD", "D7K02CD", "D7L00CD", "D7P02CD", "D7Q02CD", "D7S02CD", "D7T02CD", "D7U01CD", "D7V02CD", "D7W02CD", 
                        "D7X00CD", "D7Z04CD", "DA300CD", "DA401CD", "DA516CD", "DA639CD", "DA702CD", "DAB00CD", "DAE00CD", "DAF01CD", "DAGCDCD", 
                        "DAH02CD", "DAJ01CD", "DAK06CD", "DAL45CD", "DAO05CD", "DAP01CD", "DAQ00CD", "DAR29CD", "DAS03CD", "DAU02CD", "DAZ10CD", 
                        "DBF01CD", "DBJ03CD", "DBK60CD", "DBS00CD", "DBU11CD", "DCD00CD", "DCF14CD", "DCG18CD", "DCK04CD", "DCL12CD", "DCN04CD", 
                        "DCO01CD", "DCP01CD", "DCQ06CD", "DCU22CD", "DCX01CD", "DD429CD", "DD606CD", "DDA41CD", "DDC00CD", "DDD04CD", "DDE02CD", 
                        "DDG00CD", "DDH82CD", "DDI00CD", "DDJ03CD", "DDO01CD", "DDR07CD", "DDT00CD", "DDX02CD", "DDY23CD", "DDZI7CD", "DE201CD", 
                        "DE301CD", "DE704CD", "DE803CD", "DED44CD", "DEE37CD", "DEF00CD", "DEG23CD", "DEHEOCD", "DEJ06CD", "DEK08CD", "DEL01CD", 
                        "DENWWCD", "DES03CD", "DEZZZCD", "DFG12CD", "DFH05CD", "DFI08CD", "DFT02CD", "DFU00CD", "DFX00CD", "DGA01CD", "DGH01CD", 
                        "DGMAZCD", "DGQ22CD", "DGY08CD", "DGZ00CD", "DHB39CD", "DHE00CD", "DHG06CD", "DHJ00CD", "DHM00CD", "DHU24CD", "DHY03CD", 
                        "DI202CD", "DI301CD", "DI402CD", "DI501CD", "DI604CD", "DI702CD", "DI801CD", "DI901CD", "DIB01CD", "DIF10CD", "DIM18CD", 
                        "DIN07CD", "DIO02CD", "DIP03CD", "DIQ02CD", "DIT14CD", "DIU16CD", "DIW00CD", "DJA25CD", "DJB04CD", "DJD02CD", "DJQ00CD", 
                        "DJY11CD", "DK303CD", "DK906CD", "DKU03CD", "DKX41CD", "DL311CD", "DL600CD", "DL700CD", "DL801CD", "DL900CD", "DLA10CD", 
                        "DLB13CD", "DLD00CD", "DLE12CD", "DLI16CD", "DLN03CD", "DLV02CD", "DLW02CD", "DLX54CD", "DLZ06CD", "DMG08CD", "DMH00CD", 
                        "DMI61CD", "DMJAKCD", "DMO13CD", "DMW13CD", "DN100CD", "DN400CD", "DN510CD", "DN706CD", "DN803CD", "DN904CD", "DNA09CD", 
                        "DNB08CD", "DNC05CD", "DNF15CD", "DNG00CD", "DNH01CD", "DNK05CD", "DNM00CD", "DNN01CD", "DNR00CD", "DO103CD", "DO301CD", 
                        "DO409CD", "DO506CD", "DO813CD", "DO906CD", "DOA04CD", "DOCADCD", "DOD02CD", "DOK00CD", "DOL11CD", "DOP01CD", "DOR03CD", 
                        "DOS01CD", "DOY25CD", "DPDZCCD", "DPKADCD", "DPLNVCD", "DPQ02CD", "DPR04CD", "DPS02CD", "DPY13CD", "DQA05CD", "DQB48CD", 
                        "DQC00CD", "DQF00CD", "DQH20CD", "DQJ04CD", "DQK15CD", "DQS10CD", "DQT00CD", "DQV03CD", "DQX01CD", "DRA01CD", "DRC71CD", 
                        "DRE24CD", "DRG40CD", "DRH14CD", "DRI00CD", "DRJ05CD", "DRK05CD", "DRP02CD", "DRQ01CD", "DRS19CD", "DRT21CD", "DRU20CD", 
                        "DRZ89CD", "DSB00CD", "DSD04CD", "DSH02CD", "DSO01CD", "DSP16CD", "DTC00CD", "DTG09CD", "DTJ02CD", "DUB24CD", "DUC00CD", 
                        "DUE05CD", "DUF01CD", "DUR00CD", "DUV37CD", "DUW00CD", "DUZ19CD", "DVD09CD", "DVF37CD", "DVH37CD", "DVKAICD", "DVO01CD", 
                        "DVQ72CD", "DVS05CD", "DVU01CD", "DVW00CD", "DVX23CD", "DWAICCD", "DXA00CD", "DXC11CD", "DXD04CD", "DXF00CD", "DXG24CD", 
                        "DXQAZCD", "DXU00CD", "DXZ01CD", "DYB01CD", "DYC00CD", "DYE01CD", "DYH02CD", "DYI45CD", "DYK13CD", "DYM25CD", "DYP00CD", 
                        "DYQ02CD", "DYR22CD", "DYT31CD", "DYU03CD", "DYV19CD", "DYW25CD", "DZE34CD", "DZICUCD", "DZJFVCD", "DZVNICD", "DZZ0JCD", 
                        "T1AADG", "T1BADG", "T9AADG", "T9BADG", "T9CADG", "T9DADG", "NEW1CD", "NEW2CD"]
                ]
            ]
        ];
        return $array;
    }

    public function addXPVehicle(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        if (!$this->brandHelper->isSsdpBrand($vehicleDto->getBrand())) {
            $this->logger->error(__METHOD__ . ' : Error brand not SSDP');
            return new ErrorResponse('Brand not supported', Response::HTTP_BAD_REQUEST);
        }
        $vehicle = $this->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
        if ($vehicle) {
            $this->logger->error(__METHOD__ . ' : Error vehicle already exist');
            return new ErrorResponse('Vehicle already exist', Response::HTTP_BAD_REQUEST);
        }
        // call corvet
        $corvetData = $this->getCorvetData($vehicleDto->getVin(), $vehicleDto->getBrand());
        //retrive lcdv
        $lcdv = $this->getLcdv($corvetData);
        if (!$lcdv) {
            $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
            return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
        }
        // 
        $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
        $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
        $vehicleSettings = $this->findVehicleLabelByLcdv($lcdv);
        //call mongoDb vehicleLabel to get label
        $label = $vehicleSettings['label'] ?? null;
        $isO2x = $vehicleSettings['isO2x'] ?? false;
        if (!$label) {
            $this->logger->error(__METHOD__ . ' : Error label not found while calling mongoDb vehicleLabel');
            return new ErrorResponse('Label Not found ', Response::HTTP_NOT_FOUND);
        }
        $visualResponse = $this->visual3DManager->loadImages(
            $lcdv,
            $vehicleDto->getBrand(),
            $vehicleDto->getSource(),
            $this->getDataFromAttributes($allAttributes)
        );

        $visualUrl = $visualResponse['data'] ?? null;

        //call mongodb to get psaId = ACNT
        $psaId = $this->getPsaId($vehicleDto->getUserId(), $vehicleDto->getBrand());
        if (!$psaId) {
            $this->logger->error(__METHOD__ . ' : Error psaId not found while calling mongoDb userData');
            return new ErrorResponse('PsaId not found ', Response::HTTP_NOT_FOUND);
        }

        $siteCode = RefreshVehicleHelper::getSiteCode($vehicleDto->getBrand(), $vehicleDto->getCountry(), $vehicleDto->getSource());
        //call C@ to get ticket
        $ticket = $this->getTicket($psaId, $siteCode);
        if (!$ticket) {
            $this->logger->error(__METHOD__ . ' : Error Ticket not found while calling C@');
            return new ErrorResponse('Ticket not found ', Response::HTTP_NOT_FOUND);
        }

        // add vehicle to customer@
        $response = $this->addVehicleInCustomerAt(
            $vehicleDto->getVin(),
            $label,
            $lcdv,
            $ticket,
            $siteCode,
            $vehicleDto->getLanguage()
        );

        if (!$response) {
            $this->logger->error(__METHOD__ . ' : Error while adding vehicle in C@ VIN : ' . $vehicleDto->getVin());
            return new ErrorResponse('Error while adding vehicle in C@ ', Response::HTTP_BAD_REQUEST);
        }
        $vehicleOutputDto = new AddVehicleOutputDTO();
        $corvertAttributs = $this->getManagedAttributes($allAttributes);
        $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
        $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
        $vehicleOutputDto->setAddStatus('COMPLETE');
        $sdp = $this->getSdp($vehicleSettings['sdp']);
        $vehicleOutputDto->setSdp($sdp);
        $vehicleOutputDto->setWarrantyStartDate($vehicleData['DATE_DEBUT_GARANTIE'] ?? '');
        $vehicleOutputDto->setRegTimestamp(time());
        $vehicleOutputDto->setType($vehicleType);
        $vehicleOutputDto->setLabel($label);
        $vehicleOutputDto->setLcdv($lcdv);
        $vehicleOutputDto->setVisual($visualUrl);
        // $vehicleOutputDto->setFeaturesCode($this->featureCodeService->getFeaturesCode($lcdv, $corvertAttributs));
        $vehicleOutputDto->setIsOrder(false);
        $vehicleOutputDto->setMake($vehicleDto->getBrand());
        $vehicleOutputDto->setMarket($vehicleDto->getCountry());
        $vehicleOutputDto->setCulture(CultureHelper::createCulture($vehicleDto->getLanguage(), $vehicleDto->getCountry()));
        $vehicleOutputDto->setIsO2X($isO2x);
        $vehicleOutputDto->setLastUpdate(time());
        // calling mongoDb to push new vehicle
        $this->updateVehicleData($vehicleDto, $vehicleOutputDto);
        return new SuccessResponse("Vehicle added Successfully", Response::HTTP_CREATED);
    }

    public function editVehicle(EditVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
        try {
            $vehicleData = $this->getVehicle($vehicleDto->getUserId(), $vehicleDto->getVin());
            if (!$vehicleData) {
                return new ErrorResponse('Vehicle not exists', Response::HTTP_NOT_FOUND);
            }

            // Extract ODM Vehicle object from the response
            $vehicleObject = $vehicleData['vehicle'][0] ?? null;
            if (!$vehicleObject) {
                return new ErrorResponse('Vehicle object not found', Response::HTTP_NOT_FOUND);
            }

            // Get vehicle properties from ODM Vehicle object
            $sdp = $vehicleObject->getStatus(); // ODM uses status field instead of sdp
            $shortLabel = $vehicleObject->getLabel() ?? ''; // ODM uses label field instead of shortLabel
            $picture = $vehicleObject->getPicture() ?? ''; // ODM Vehicle now has picture field

            // Get current mileage from ODM Vehicle object
            $currentMileage = 0;
            $mileageData = $vehicleObject->getMileage();
            if ($mileageData) {
                $currentMileage = $mileageData->getValue() ?? 0;
            }

            // Get current nickName and licencePlate from ODM Vehicle object
            $currentNickName = $vehicleObject->getNickName() ?? ''; // ODM Vehicle now has nickName field
            $currentLicencePlate = $vehicleObject->getRegistrationNumber() ?? '';

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle data extracted from ODM', [
                'vin' => $vehicleObject->getVin(),
                'sdp' => $sdp,
                'shortLabel' => $shortLabel,
                'currentMileage' => $currentMileage,
                'currentNickName' => $currentNickName,
                'currentLicencePlate' => $currentLicencePlate,
            ]);

            $vehicleDto->setCommercialName($shortLabel);
            $vehicleDto->setPictureUrl($picture);

            if ($sdp != RefreshVehicleInterface::SDP_SSDP) {
                return new ErrorResponse('Vehicle not managed in user repository', Response::HTTP_NOT_FOUND);
            }

            $customerId = $vehicleData['userDbId'] ?? null;
            if (!$customerId) {
                return new ErrorResponse('User not existe in user repository', Response::HTTP_NOT_FOUND);
            }

            $vehicleDto->setCustomerId($customerId);
            $response = $this->systemUserDBService->updateCustomerGarage($vehicleDto);

            if ($response->getCode() == Response::HTTP_OK) {
                $vehicleDto->setMileageDate(time());
                $vehicleDto->setMileage($vehicleDto->getMileage() !== null ? (int) $vehicleDto->getMileage() : $currentMileage);
                $vehicleDto->setNickName($vehicleDto->getNickName() ?: $currentNickName);
                $vehicleDto->setLicencePlate($vehicleDto->getLicencePlate() ?: $currentLicencePlate);
                $this->updateVehicle($vehicleDto);
                return new SuccessResponse(EditVehicleMapper::map($vehicleDto), Response::HTTP_OK);
            }

            return (new ErrorResponse($response->getData()['error']['message'] ?? '', $response->getCode()))->setErrors($response->getData()['error']['errors'] ?? []);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . 'Catched Exception VehicleManager::editVehicle ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function getSdp(?string $vehicleLabelSdp = null)
    {
        $sdp = RefreshVehicleInterface::SDP_CVMP;
        if ($vehicleLabelSdp == RefreshVehicleInterface::SDP_SSDP) {
            $sdp = RefreshVehicleInterface::SDP_SSDP;
        }

        return $sdp;
    }

    public function setEligibilityFromContracts(?array $subscriptions): array
    {
        $eligibilities = [];
        foreach ($subscriptions as $subscription) {

            $eligibility = $subscription['type'] ?? '';
            if (in_array($eligibility, ['remotelev_phev', 'remotelev_bev', 'bev', 'phev'])) {
                $eligibility = self::ELIGIBILITY_REMOTELEV;
            } elseif ($eligibility === 'navco') {
                $eligibility = self::ELIGIBILITY_NAC;
            }

            if ($eligibility && !in_array($eligibility, $eligibilities)) {
                $eligibilities[] = $eligibility;
            }
        }
        return $eligibilities;
    }

    public function getVehicleType(string $vehicleType): int
    {
        switch (strtoupper($vehicleType)) {
            case "ICE":
                return 0;
            case "HEV":
                return 2;
            case "PHEV":
                return 3;
            case "BEV":
                return 4;
            case "MHEV":
                return 5;
            case "HFCV":
                return 6;
            default:
                return 10;
        }
    }

    public function removeNullValues(array $array): array
    {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = $this->removeNullValues($value);
            }
            if (is_null($array[$key])) {
                unset($array[$key]);
            }
        }
        return $array;
    }

    public function mapVehicleInfoXPFormat(array $vehicle, array $catalogResponse, array $subscriptionResponse, array $productsStatus, array $featureCodes): array
    {
        $type = $this->getVehicleType($vehicle['type'] ?? '');
        $vehicleXPFormat = [
            'vehicleInfo' => [
                'vin' => $vehicle['vin'] ?? '',
                'lcdv' => $vehicle['lcdv'] ?? '',
                'visual' => $vehicle['picture'] ?? '',
                'short_label' => $vehicle['nickName'] ?? $vehicle['shortLabel'],
                'nickname' => $vehicle['nickName'] ?? '',
                'warranty_start_date' => $vehicle['warrantyStartDate'] ?? null,
                'attributes' => $vehicle['attributes'] ?? [],
                'type_vehicle' => $type,
                'mileage' => $vehicle['mileage'] ?? new stdClass(),
                'versionId' => $vehicle['versionId'] ?? '',
                'brand' => $vehicle['make'] ?? '',
                'subMake' => $vehicle['subMake'] ?? '',
                'country' => $vehicle['market'] ?? '',
                'regTimeStamp' => $vehicle['regTimeStamp'] ?? '',
                'year' => $vehicle['year'] ?? '',
                'sdp' => $vehicle['sdp'] ?? '',
                'isOrder' => $vehicle['isOrder'] ?? false,
                'features' => $featureCodes ?? new stdClass(),
                'addStatus' => $vehicle['addStatus'] ?? '',
                'isO2x' => $vehicle['isO2x'] ?? '',

                'criteriaValue' => $vehicle['criteriaValue'] ?? null,
                'plugType' => $vehicle['plugType'] ?? null
            ],
            'eligibility' => $this->setEligibilityFromContracts($subscriptionResponse),
            'vehicleProducts' => [
                'productsCatalog' => $catalogResponse,
                'purchasedProducts' => $subscriptionResponse,
                'productGroupNameStatus' => empty($productsStatus) ? new stdClass() : $productsStatus
            ],
            'settingsUpdate' => $vehicle['lastUpdate'] ?? null
        ];

        $vehicleXPFormat = $this->removeNullValues($vehicleXPFormat);
        return $vehicleXPFormat;
    }

    public function getVehicleDetail(
        string $userId,
        string $critariaValue,
        string $language,
        string $country,
        string $critariaKey = 'vin',
        string $source = 'APP'
    ) {
        try {
            $vehicleResponse = $this->getVehicle($userId, $critariaValue, $critariaKey);

            if (!$vehicleResponse) {
                $this->logger->error(__METHOD__ . ' : Error vehicle not exist');
                return new ErrorResponse('Vehicle not exist', Response::HTTP_NOT_FOUND);
            }

            $vehicleObject = $vehicleResponse['vehicle'][0] ?? null;
            if (!$vehicleObject) {
                $this->logger->error(__METHOD__ . ' : Error vehicle object not found');
                return new ErrorResponse('Vehicle object not found', Response::HTTP_NOT_FOUND);
            }

            // Convert ODM Vehicle object to array for backward compatibility
            $vehicle = [
                'vin' => $vehicleObject->getVin(),
                'brand' => $vehicleObject->getBrand(),
                'sdp' => $vehicleObject->getStatus(), // ODM uses status field instead of sdp
                'model' => $vehicleObject->getModel(),
                'versionId' => $vehicleObject->getVersionId(),
                'criteriaValue' => $critariaValue, // Add the criteria value used to find the vehicle
                // Additional fields from ODM Vehicle document
                'shortLabel' => $vehicleObject->getShortLabel(),
                'nickName' => $vehicleObject->getNickName(),
                'picture' => $vehicleObject->getPicture(),
                'lastUpdate' => $vehicleObject->getLastUpdate(),
                'year' => $vehicleObject->getYear(),
                'country' => $vehicleObject->getCountry(),
                'market' => $vehicleObject->getMarket(),
                'regTimeStamp' => $vehicleObject->getRegTimeStamp(),
                'warrantyStartDate' => $vehicleObject->getWarrantyStartDate(),
                'make' => $vehicleObject->getMake(),
                'subMake' => $vehicleObject->getSubMake(),
                'enrollmentStatus' => $vehicleObject->getEnrollmentStatus(),
                'connectorType' => $vehicleObject->getConnectorType(),
                'type' => $vehicleObject->getType(),
                'addStatus' => $vehicleObject->getAddStatus(),
                // Mileage data
                'mileage' => $vehicleObject->getMileageAsArray(),
            ];

            $sdp = $vehicle['sdp'] ?? '';
            $vin = $vehicle['vin'] ?? '';
            $brand = $vehicle['brand'] ?? '';
            $userDbId = $vehicleResponse['userDbId'] ?? null;

            if (!$userDbId) {
                $this->logger->error(__METHOD__ . ' : Error userDbId not exist');
                return new ErrorResponse('UserDbId not exist', Response::HTTP_NOT_FOUND);
            }

            // Validate brand before calling Corvet API
            if (empty($brand)) {
                $this->logger->error(__METHOD__ . ' : Error vehicle brand is empty', [
                    'vin' => $vin,
                    'userId' => $userId,
                    'vehicleData' => $vehicle
                ]);
                return new ErrorResponse('Vehicle brand is required for Corvet API call', Response::HTTP_BAD_REQUEST);
            }

            $corvetData = $this->getCorvetData($vin, $brand);
            $this->logger->info(__METHOD__ . ' => Response from corvet service', ['corvetData' => $corvetData]);

            if (array_key_exists('error', $corvetData)) {
                $errorMessage = is_array($corvetData['error']) ? json_encode($corvetData['error']) : (string)$corvetData['error'];
                $this->logger->error(__METHOD__ . ' : Error while calling corvet service', [
                    'error' => $corvetData['error'],
                    'errorMessage' => $errorMessage
                ]);
                return new ErrorResponse('Error while calling corvet service: ' . $errorMessage, Response::HTTP_INTERNAL_SERVER_ERROR);
            }

            // Check if vehicle exists in Corvet
            $vehicleExists = $corvetData['VEHICULE']['@attributes']['Existe'] ?? 'N';
            if ($vehicleExists === 'N') {
                $this->logger->warning(__METHOD__ . ' : Vehicle does not exist in Corvet system', [
                    'vin' => $vin,
                    'brand' => $brand,
                    'corvetResponse' => $corvetData
                ]);
                return new ErrorResponse('Vehicle not found in Corvet system', Response::HTTP_NOT_FOUND);
            }

            $lcdv = $this->getLcdv($corvetData);
            if (!$lcdv) {
                $this->logger->error(__METHOD__ . ' : LCDV not found in corvet data', [
                    'vin' => $vin,
                    'brand' => $brand,
                    'corvetData' => $corvetData
                ]);
                return new ErrorResponse('Vehicle LCDV not found', Response::HTTP_NOT_FOUND);
            }

            $vehicleData = $corvetData['VEHICULE']['DONNEES_VEHICULE'] ?? [];
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->getManagedAttributes($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
            if (!isset($vehicle['plugType'])) {
                $vehiclePlugTypeNumber = VehicleTypeEntities::getType('D7Q', $corvertAttributs);
                $vehicle['plugType'] = VehicleTypeEntities::PLUG_TYPES[$vehiclePlugTypeNumber] ?? '';
            }
            $vehicleSettings = $this->findVehicleLabelByLcdv($lcdv);
            $this->logger->info(__METHOD__ . " => Response from findVehicleLabelByLcdv", ['vehicleSettings' => $vehicleSettings]);
            $isO2x = $vehicleSettings['isO2x'] ?? false;
            $label = $vehicleSettings['label'] ?? '';
            $vehicle['label'] = $label;
            $vehicle['lcdv'] = $lcdv;
            $vehicle['isO2x'] = $isO2x;
            $vehicle['attributes'] = $corvertAttributs;
            $params = [
                'userId' => $userId,
                'vin' => $vin,
                'brand' => $brand,
                'country' => $country,
                'language' => $language,
                'source' => $source,
                'userDbId' => $userDbId
            ];
            $target = 'B2C';

            $catalogResponse = $this->catalogManager->getCatalog($params);
            if ($catalogResponse->getCode() !== Response::HTTP_OK) {
                $this->logger->error(__METHOD__ . ' : Error while calling catalog service');
                return $catalogResponse;
            }
            $this->logger->info("".__METHOD__." => Response from catalog service --> " . ['catalogResponse' => $catalogResponse->getData()]);
            $catalogResponse = $catalogResponse->getData() ?? [];
            $productsStatus = array_fill_keys(array_column($catalogResponse, 'id', 'id'), 'disabled');

            $subscriptionResponse = $this->subscriptionManager->getSubscription($userDbId, $vin, $target, $brand, $country, $language, $source);
            if ($subscriptionResponse->getCode() !== Response::HTTP_OK) {
                $this->logger->error(__METHOD__ . ' : Error while calling subscription service');
                return $subscriptionResponse;
            }
            $this->logger->info("".__METHOD__." => Response from subscription service --> " . ['subscriptionResponse' => $subscriptionResponse->getData()]);
            $subscriptionResponse = $subscriptionResponse->getData() ?? [];
            $productsStatus = array_merge($productsStatus, array_fill_keys(array_map('strtolower', array_column($subscriptionResponse, 'type', 'type')), 'enabled'));

            //Feature codes
            $featureCodes  = $this->getUpdatedFeatureCodes($vehicle, $userId, $vin, $lcdv, $vehicleType, $corvertAttributs, $userDbId);

            $vehicleXPFormat = $this->mapVehicleInfoXPFormat($vehicle, $catalogResponse, $subscriptionResponse, $productsStatus, $featureCodes);
            return new SuccessResponse($vehicleXPFormat ?? [], Response::HTTP_OK);
        } catch (\Exception $e) {
            $this->logger->error('=> ' . __METHOD__ . 'Catched Exception VehicleManager::getVehicleDetail ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    private function updateVehicleData(
        AddVehicleInputDTO $vehicleDto,
        AddVehicleOutputDTO $vehicleOutputDto
    ): WSResponse {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle data using ODM', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
            ]);

            // Create ODM Vehicle document from the data
            $vehicleDataArray = [
                'vin' => $vehicleDto->getVin(),
                'brand' => $vehicleDto->getBrand(),
                'shortLabel' => $vehicleOutputDto->getLabel(),
                'modelDescription' => $vehicleOutputDto->getLabel(),
                'versionId' => $vehicleOutputDto->getLcdv(),
                'featureCode' => $vehicleOutputDto->getFeaturesCode(),
                'sdp' => $vehicleOutputDto->getSdp(),
                'type' => $vehicleOutputDto->getType(),
                'regTimeStamp' => $vehicleOutputDto->getRegTimestamp(),
                'lastUpdate' => $vehicleOutputDto->getLastUpdate(),
                'addStatus' => $vehicleOutputDto->getAddStatus(),
                'isOrder' => $vehicleOutputDto->getIsOrder(),
                'country' => $vehicleDto->getCountry(),
                'language' => $vehicleDto->getLanguage(),
                'visual' => $vehicleOutputDto->getVisual(),
                'picture' => $vehicleOutputDto->getVisual(),
                'make' => $vehicleOutputDto->getMake(),
                'market' => $vehicleOutputDto->getMarket(),
                'warrantyStartDate' => $vehicleOutputDto->getWarrantyStartDate(),
                'featureCodeExpiry' => $vehicleOutputDto->getFeatureCodeExpiry(),
                'connectorType' => $vehicleOutputDto->getConnectorType(),
                'plugType' => $vehicleOutputDto->getPlugType(),
                'isO2x' => $vehicleOutputDto->getIsO2X(),
                'culture' => $vehicleOutputDto->getCulture(),
                'year' => $vehicleOutputDto->getYear(),
                'nickName' => $vehicleOutputDto->getNickName(),
                'enrollmentStatus' => $vehicleOutputDto->getEnrollmentStatus(),
                'subMake' => $vehicleOutputDto->getSubMake(),
                // Add mileage data
                'mileage' => [
                    'date' => time(),
                    'value' => (int)$vehicleDto->getMileage()
                ],
            ];

            $this->logger->debug(__METHOD__ . ' Vehicle data being passed to ODM conversion', [
                'vehicleDataArray' => $vehicleDataArray
            ]);

            $vehicle = $this->userDataService->convertLegacyVehicleToODM($vehicleDataArray);

            // Add vehicle using UserDataService
            $success = $this->userDataService->addVehicleToUserDocument($vehicleDto->getUserId(), $vehicle);

            if ($success) {
                return new WSResponse(Response::HTTP_OK, 'Vehicle added successfully');
            } else {
                return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, 'Failed to add vehicle');
            }

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle data', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
                'exception' => $e->getMessage()
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, $e->getMessage());
        }
    }

    private function addVehicleInCustomerAt(
        string $vin,
        string $label,
        string $lcdv,
        string $ticket,
        string $siteCode,
        string $language
    ): ?array {

        $data = [
            'VEH_VIN' => $vin,
            'VEH_CLASS_LABEL' => $label,
            'VEH_LCDV' => $lcdv,
            'VEH_VIS' => substr($vin, 9),
        ];
        // insert into- customer@
        $response = $this->systemUserDataClient->addVehicle($ticket, $siteCode, $language, $data);

        return $response->getData()['success'] ?? null;
    }

    /**
     * @param string $psaId
     * @param string $siteCode
     * 
     * @return string|null
     */
    private function getTicket(
        string $psaId,
        string $siteCode
    ): ?string {
        // get ticket from user data
        $userdata = $this->systemUserDataClient->getV1CatTicket($psaId, $siteCode);
        return $userdata->getData()['success']['ticket'] ?? null;
    }

    /**
     * @param string $userId
     * @param string $brand
     *
     * @return string|null
     */
    private function getPsaId(
        string $userId,
        string $brand
    ): ?string {
        $psaId = $this->userDataService->getPsaIdByUserAndBrand($userId, $brand);
        return RefreshVehicleHelper::parsePsaId($psaId ?? '');
    }

    private function getLcdv(
        ?array $corvetData
    ): ?string {
        try {
            return $corvetData['VEHICULE']['DONNEES_VEHICULE']['LCDV_BASE'] ?? null;
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' : Error while getting lcdv from corvet data', ['corvetData' => $corvetData]);
            return null;
        }
    }

    private function getCorvetData(
        string $vin,
        string $brand
    ): ?array {
        return $this->corvetService->getData($vin, $brand);
    }

    private function getVehicle(string $userId, string $critariaValue, string $critariaKey = 'vin'): ?array
    {
        $userData = $this->userDataService->findUserById($userId);
        if (!$userData) {
            return null;
        }

        $vehicles = $userData->getVehicles();
        $vehicle = null;

        // Enhanced vehicle lookup with multi-strategy approach
        foreach ($vehicles as $v) {
            // Check multiple possible identifiers for the vehicle
            // 1. Check VIN (primary identifier)
            // 2. Check document ID (MongoDB 'id' field) for 'id' criteria
            // 3. Check other standard criteria

            if ($critariaKey === 'id') {
                // For 'id' criteria, check both VIN and documentId
                if ($v->getDocumentId() === $critariaValue) {
                    $vehicle = $v;
                    $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Found vehicle by ID match', [
                        'critariaValue' => $critariaValue,
                        'matchType' => $v->getVin() === $critariaValue ? 'VIN' : 'documentId',
                    ]);
                    break;
                }
            } else {
                // For other criteria, use the original matching logic
                $value = match($critariaKey) {
                    'vin' => $v->getVin(),
                    'brand' => $v->getBrand(),
                    'model' => $v->getModel(),
                    'versionId' => $v->getVersionId(),
                    default => null
                };

                if ($value === $critariaValue) {
                    $vehicle = $v;
                    break;
                }
            }
        }

        if ($vehicle) {
            // Check if vehicle has SSDP status or no status (for ODM compatibility)
            $status = $vehicle->getStatus();
            if ($status === 'SSDP' || empty($status)) {
                // For ODM compatibility: userDbId might not exist in ODM model
                // Use userId as fallback or derive from PSA ID if available
                $userDbId = $this->extractUserDbIdFromODMUserData($userData);

                return [
                    'vehicle' => [$vehicle], // Maintain array structure for backward compatibility
                    'userDbId' => $userDbId,
                ];
            }
        }

        // If no vehicle found and criteria is 'id', try to fix vehicles without documentId
        if (!$vehicle && $critariaKey === 'id') {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No vehicle found by ID', [
                'userId' => $userId,
                'critariaValue' => $critariaValue,
            ]);
        }

        return null;
    }

    private function getUserDbId(string $userId): ?string
    {
        $userData = $this->userDataService->findUserById($userId);
        return $this->extractUserDbIdFromODMUserData($userData);
    }

    private function findVehicleLabelByLcdv(?string $lcdv): ?array
    {
        $response = $this->vehicleLabelService->getVehicleLabelDocumentByLcdv($lcdv);
        return json_decode($response->getData(), true)['documents'][0] ?? [];
    }

    public static function getDataFromAttributes(array $attributes): ?array
    {
        $data = [];
        foreach ($attributes as $attribute) {
            if (preg_match('/^P(.{4})|^D(.{4})CP$/i', $attribute)) {
                $data[] = substr($attribute, 1, 4);
            }
        }

        return $data;
    }

    private function getManagedAttributes(array $attributes): ?array
    {
        $managedAttributes = [];
        foreach ($attributes as $attribute) {
            switch (substr($attribute, 0, 3)) {
                case 'DCX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DXD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DCD':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRE':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DRC':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DMW':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DVQ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DJY':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7Q':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D7K':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DME':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DE2':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DZZ':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DLX':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DO9':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'D32':
                    $managedAttributes[] = trim($attribute);
                    break;
                case 'DYR':
                    if (substr($attribute, 3, 2) == "17") {
                        $managedAttributes[] = trim($attribute);
                    }
                    break;
            }
        }
        return $managedAttributes;
    }

    private function updateVehicle(
        EditVehicleInputDTO $vehicleDto
    ): bool {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updating vehicle using ODM', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
                'mileage' => $vehicleDto->getMileage(),
                'nickName' => $vehicleDto->getNickName(),
                'licencePlate' => $vehicleDto->getLicencePlate(),
            ]);

            $userData = $this->userDataService->findUserById($vehicleDto->getUserId());
            if (!$userData) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' User not found', [
                    'userId' => $vehicleDto->getUserId()
                ]);
                return false;
            }

            $vehicle = $userData->findVehicleByVin($vehicleDto->getVin());
            if (!$vehicle) {
                $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Vehicle not found', [
                    'userId' => $vehicleDto->getUserId(),
                    'vin' => $vehicleDto->getVin()
                ]);
                return false;
            }

            // Update license plate (registration number)
            if ($vehicleDto->getLicencePlate()) {
                $vehicle->setRegistrationNumber($vehicleDto->getLicencePlate());
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updated license plate', [
                    'vin' => $vehicleDto->getVin(),
                    'licencePlate' => $vehicleDto->getLicencePlate()
                ]);
            }

            // Update mileage using ODM MileageData object
            if ($vehicleDto->getMileage() !== null) {
                $mileageData = $vehicle->getMileage();
                if (!$mileageData) {
                    // Create new MileageData object if it doesn't exist
                    $mileageData = new MileageData();
                    $vehicle->setMileage($mileageData);
                }

                $mileageData->setValue((int) $vehicleDto->getMileage());

                // Set timestamp which will auto-set the date
                $timestamp = $vehicleDto->getMileageDate() ?? time();
                $mileageData->setTimestamp($timestamp);

                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updated mileage', [
                    'vin' => $vehicleDto->getVin(),
                    'mileage' => $vehicleDto->getMileage(),
                    'mileageTimestamp' => $timestamp
                ]);
            }

            // Update nickName using ODM Vehicle object
            if ($vehicleDto->getNickName()) {
                $vehicle->setNickName($vehicleDto->getNickName());
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Updated nickName', [
                    'vin' => $vehicleDto->getVin(),
                    'nickName' => $vehicleDto->getNickName()
                ]);
            }

            $this->userDataService->saveUserData($userData);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Successfully updated vehicle', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin()
            ]);

            return true;

        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error updating vehicle', [
                'userId' => $vehicleDto->getUserId(),
                'vin' => $vehicleDto->getVin(),
                'exception' => $e->getMessage()
            ]);

            return false;
        }
    }


    /**
     * create or update vehicle data function.
     */
    public function deleteVehicle(string $userId, string $vin): ResponseArrayFormat
    {
        try {
            // First check if user exists
            $userData = $this->userDataService->findUserById($userId);
            if (!$userData) {
                $this->logger->error(__METHOD__ . ' User not found', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                return new ErrorResponse('User not found', Response::HTTP_NOT_FOUND);
            }

            $dbUserData = $this->userDataService->getVehicleAndUserDBIdByUserIdAndVin($userId, $vin);
            if ($dbUserData != null && isset($dbUserData)) {
                $deleteResponse = $this->userDataService->removeUserSSDPVehicles($userId, $vin);
                if ($deleteResponse) {
                    $this->logger->info(
                        'Deleted successfully.'
                    );
                    $userDBResponse = $this->systemUserDBService->deleteCustomerVehicle($dbUserData["userDbId"], $vin);
                    if (Response::HTTP_OK !== $userDBResponse->getCode() && Response::HTTP_NO_CONTENT !== $userDBResponse->getCode()) {
                        $this->logger->error(
                            'An error has occurred while deleting Vehicle from User in SSDP',
                            [
                                'userDbId' => $dbUserData["userDbId"],
                                'vin' => $vin
                            ]
                        );
                        $this->userDataService->addVehicleToUserDocument($userId, $dbUserData["vehicle"]);
                        return new ErrorResponse("Error while deleting the vin in SSDP. userId " . $userId . " vin " . $vin);
                    }
                    return new SuccessResponse("", Response::HTTP_NO_CONTENT);
                } else {
                    $this->logger->error(__METHOD__ . ' Failed to delete vehicle from user data', [
                        'userId' => $userId,
                        'vin' => $vin
                    ]);
                    return new ErrorResponse('Failed to delete vehicle from user data', Response::HTTP_INTERNAL_SERVER_ERROR);
                }
            } else {
                $this->logger->error(__METHOD__ . ' Vehicle not found for deletion', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                return new ErrorResponse('Vehicle not found', Response::HTTP_NOT_FOUND);
            }
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Catched Exception ' . $e->getMessage());
            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    /**
     * Get vehicle features
     * 
     * @param string $userId User ID
     * @param string $vin Vehicle Identification Number
     * 
     * @return ErrorResponse|SuccessResponse
     */
    public function getVehicleFeatures(string $userId, string $vin)
    {
        try {
            // Verify the vehicle belongs to the user
            $vehicleResponse = $this->userDataService->getVehicle($userId, $vin);

            if (!$vehicleResponse) {
                $this->logger->error(__METHOD__ . ' : Error vehicle not exist');
                return new ErrorResponse('Vehicle does not exist', Response::HTTP_NOT_FOUND);
            }

            $vehicleObject = $vehicleResponse['vehicle'][0] ?? null;
            $userDbId = $vehicleResponse['userDbId'] ?? null;

            if (!$vehicleObject) {
                return new ErrorResponse('Vehicle data not found', Response::HTTP_NOT_FOUND);
            }

            // Convert ODM Vehicle object to array for backward compatibility
            $vehicle = [
                'vin' => $vehicleObject->getVin(),
                'brand' => $vehicleObject->getBrand(),
                'model' => $vehicleObject->getModel(),
                'versionId' => $vehicleObject->getVersionId(),
                'status' => $vehicleObject->getStatus(),
                'featureCode' => $vehicleObject->getFeatureCode() ?? [],
                'featureCodeExpiry' => null, // ODM Vehicle doesn't have this field
                'type' => $vehicleObject->getType(), // Get type from ODM Vehicle object
            ];

            // Extract feature codes from the vehicle data
            $featureCodes = $vehicle['featureCode'] ?? [];
            $featureCodeExpiry = $vehicle['featureCodeExpiry'] ?? null;

            $currentTime = time();

            // Check if feature codes have expired
            if ($featureCodeExpiry === null || $currentTime > $featureCodeExpiry) {
                $this->logger->info(__METHOD__ . ' : Feature codes have expired or no expiry set, regenerating...', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'currentTime' => $currentTime,
                    'featureCodeExpiry' => $featureCodeExpiry
                ]);
                
                $brand = $vehicle['brand'] ?? null;
                $vehicleType = $vehicle['type'] ?? null;

                // Validate brand before calling Corvet API
                if (empty($brand)) {
                    $this->logger->error(__METHOD__ . ' : Error vehicle brand is empty for feature code generation', [
                        'vin' => $vin,
                        'userId' => $userId,
                        'vehicleData' => $vehicle
                    ]);
                    return new ErrorResponse('Vehicle brand is required for feature code generation', Response::HTTP_BAD_REQUEST);
                }

                // If vehicleType is null, we'll derive it from Corvet data later
                if (empty($vehicleType)) {
                    $this->logger->info(__METHOD__ . ' : Vehicle type is null, will derive from Corvet data', [
                        'vin' => $vin,
                        'userId' => $userId,
                        'brand' => $brand
                    ]);
                }

                //Call corvet API
                $corvetData = $this->getCorvetData($vin, $brand);
                // $corvetData = $this->mockCorvetData(); //TODO remove this 
                $lcdv = $this->getLcdv($corvetData);
                if (!$lcdv) {
                    $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                    return new ErrorResponse('rror lcdv not found while calling corvet, while getting updated feature codes', Response::HTTP_INTERNAL_SERVER_ERROR);
                }
                $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
                $corvertAttributs = $this->getManagedAttributes($allAttributes);

                // If vehicleType is null, derive it from Corvet data
                if (empty($vehicleType)) {
                    $vehicleTypeNumber = VehicleTypeEntities::getType($brand, $corvertAttributs);
                    $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';
                    $this->logger->info(__METHOD__ . ' : Derived vehicle type from Corvet data', [
                        'vin' => $vin,
                        'userId' => $userId,
                        'vehicleTypeNumber' => $vehicleTypeNumber,
                        'vehicleType' => $vehicleType
                    ]);
                }

                // Ensure vehicleType is not null for the service call
                if (empty($vehicleType)) {
                    $this->logger->warning(__METHOD__ . ' : Vehicle type is still empty after derivation, using default', [
                        'vin' => $vin,
                        'userId' => $userId
                    ]);
                    $vehicleType = 'UNKNOWN'; // Default fallback
                }

                // Regenerate feature codes
                $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, null, null, false, $userDbId);
               
                // get existing NON FDS feature codes
                $existingNonFDSFeatures = [];
                $currentFeatureCodes = $vehicle['featureCode'] ?? [];
                $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
                foreach ($currentFeatureCodes as $featureCode) {
                    if (in_array($featureCode['code'], $nonFdsKeys)) {
                        $existingNonFDSFeatures[] = $featureCode;
                    }
                }
                $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);

                // Update the vehicle document with new feature codes and expiry
                $this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes);
            }

            if (empty($featureCodes)) {
                $this->logger->info(__METHOD__ . ' : No feature codes found for vehicle', [
                    'userId' => $userId,
                    'vin' => $vin
                ]);
                // Return empty array instead of error to maintain consistent response structure
                return new SuccessResponse(['features' => []]);
            }

            return new SuccessResponse(['features' => $featureCodes]);
        } catch (\Exception $e) {
            $this->logger->error('Error getting  vehicle features: ' . $e->getMessage());
            return new ErrorResponse('Failed to retrieve vehicle features', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    // START::STRICTLY FOR TESTING FEATURE CODES ONLY
    public function testFeaureCodes(AddVehicleInputDTO $vehicleDto): ResponseArrayFormat
    {
            $corvetData = $this->mockCorvetData();
            $lcdv = $this->getLcdv($corvetData);
            if (!$lcdv) {
                $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                return new ErrorResponse('Vehicle LCDV Not Found', Response::HTTP_NOT_FOUND);
            }
            $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
            $corvertAttributs = $this->getManagedAttributes($allAttributes);
            $vehicleTypeNumber = VehicleTypeEntities::getType('DXD', $corvertAttributs);
            $vehicleType = VehicleTypeEntities::TYPES[$vehicleTypeNumber] ?? '';

            // Get userDbId from user data - properly extract it from ODM UserData document
            $userData = $this->userDataService->findUserById($vehicleDto->getUserId());
            $userDbId = null;
            if ($userData) {
                $userDbId = $userData->getUserDbId();
                $this->logger->info(__METHOD__ . ' : Retrieved userDbId from user data', [
                    'userId' => $vehicleDto->getUserId(),
                    'userDbId' => $userDbId
                ]);
            } else {
                $this->logger->warning(__METHOD__ . ' : User data not found, using userId as fallback for userDbId', [
                    'userId' => $vehicleDto->getUserId()
                ]);
                $userDbId = $vehicleDto->getUserId(); // Fallback to userId if user data not found
            }

            $featureCodes = $this->featureCodeService->getFeaturesCode( $vehicleDto->getUserId(), $vehicleDto->getVin(), $lcdv, $vehicleType, $corvertAttributs, null, null, true, $userDbId);
            return new SuccessResponse($featureCodes);
    }
    // END::STRICTLY FOR TESTING FEATURE CODES ONLY

    public function handleCustomerRightsUpdate(
        string $eventType, 
        string $vin, 
        string $userId, 
        string $timestamp,
        string $gigya_uid, 
        ?string $carAssociationLevel = null
    ): bool {
        try {
            $this->logger->info('Handling customer rights update', [
                'vin' => $vin,
                'userId' => $userId,
                'eventType' => $eventType,
                'timestamp' => $timestamp,
                'gigya_uid' => $gigya_uid, //userId in DB
                'carAssociationLevel' => $carAssociationLevel
            ]);
            
            // switch ($eventType) {
            //     case 'UPDATED_VEHICLE_CONTRACTS':
                    $userDocument = $this->userDataService->getVehicleAndUserIdByVin($gigya_uid, $vin);
                    if (!$userDocument) {
                        $this->logger->error(__CLASS__ . '::' . __METHOD__."No user data found for VIN: {$vin}", [
                            'vin' => $vin,
                            'gigya_uid' => $gigya_uid,
                            'userId' => $userId,
                            'eventType' => $eventType
                        ]);
                        return false;
                    }
                    $userId = $userDocument['userId'];
                    $vehicleObj = $userDocument['vehicle'];
                    $vehicleType = $vehicleObj->getType();
                    $lcdv = $vehicleObj->getVersionId();
                    $brand = $vehicleObj->getBrand();
                    $f2mcObj = $userDocument['f2mc'] ?? null;
                    $country = $vehicleObj->getCountry();
                    $userDbId = $userDocument['userDbId'] ?? null;
        
                    //Call corvet API
                    $corvetData = $this->getCorvetData($vin, $brand);
                    // $corvetData = $this->mockCorvetData(); //TODO remove this 
                    $lcdv = $this->getLcdv($corvetData);
                    if (!$lcdv) {
                        $this->logger->error(__METHOD__ . ' : Error lcdv not found while calling corvet');
                        return false;
                    }

                    $allAttributes = $corvetData['VEHICULE']['LISTE_ATTRIBUTES_7']['ATTRIBUT'] ?? [];
                    $corvertAttributs = $this->getManagedAttributes($allAttributes);
        
                    // Regenerate feature codes
                    $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, $f2mcObj, $country, false, $userDbId);

                    // get existing NON FDS feature codes
                    $existingNonFDSFeatures = [];
                    $currentFeatureCodes = $vehicleObj->getFeatureCodes() ?? [];
                    $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
                    foreach ($currentFeatureCodes as $featureCode) {
                        if (in_array($featureCode['code'], $nonFdsKeys)) {
                            $existingNonFDSFeatures[] = $featureCode;
                        }
                    }
                   
                    $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);
                    if (empty($featureCodes)) {
                        $this->logger->warning(__CLASS__ . '::' . __METHOD__."No feature codes generated for VIN: {$vin}, User ID: {$userId}");
                        return false;
                    }
            
                    if (!$this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes)) {
                        $this->logger->error(__CLASS__ . '::' . __METHOD__."Failed to update feature codes for VIN: {$vin}, User ID: {$userId}");
                        return false;
                    }
            
                    $this->logger->info(__CLASS__ . '::' . __METHOD__."Successfully updated feature codes for VIN: {$vin}, User ID: {$userId}");
                    // break;
            //     case 'CUSTOMER_RIGHTS_UPDATED':
            //         break;
            // }
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('Error handling customer rights update', [
                'exception' => $e->getMessage(),
                'vin' => $vin,
                'userId' => $userId,
                'eventType' => $eventType
            ]);
            
            return false;
        }
    }

    /**
     * Gets the most recent feature codes for a vehicle, regenerating them if expired
     * 
     * @param array $vehicle The vehicle data array
     * @param string $userId The user ID
     * @param string $vin The vehicle identification number
     * @param string $lcdv The LCDV code
     * @param string $vehicleType The vehicle type
     * @param array $corvertAttributs The corvert attributes
     * @return array The feature codes array
     */
    private function getUpdatedFeatureCodes(
        array $vehicle,
        string $userId,
        string $vin,
        string $lcdv,
        string $vehicleType,
        array $corvertAttributs,
        string $userDbId
    ): array {
        $featureCodes = $vehicle['featureCode'] ?? [];
        $featureCodeExpiry = $vehicle['featureCodeExpiry'] ?? null;
        $currentTime = time();

        // Check if feature codes have expired
        if ($featureCodeExpiry === null || $currentTime > $featureCodeExpiry) {
            $this->logger->info(__METHOD__ . ' : Feature codes have expired or no expiry set, regenerating...', [
                'userId' => $userId,
                'vin' => $vin,
                'currentTime' => $currentTime,
                'featureCodeExpiry' => $featureCodeExpiry
            ]);
            
            // Regenerate feature codes
            $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, null, null, false, $userDbId);

            // get existing NON FDS feature codes
            $existingNonFDSFeatures = [];
            $currentFeatureCodes = $vehicle['featureCode'] ?? [];
            $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
            foreach ($currentFeatureCodes as $featureCode) {
                if (in_array($featureCode['code'], $nonFdsKeys)) {
                    $existingNonFDSFeatures[] = $featureCode;
                }
            }

            $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);
            
            // Update the vehicle document with new feature codes and expiry
            $this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes);
        }
        // updating each time since webhook is not triggered for feature codes
        else {
            $this->logger->info(__METHOD__ . ' : Feature codes have are regenerating...', [
                'userId' => $userId,
                'vin' => $vin,
                'currentTime' => $currentTime,
                'featureCodeExpiry' => $featureCodeExpiry
            ]);
            
            // Regenerate feature codes
            $featureCodes = $this->featureCodeService->getFeaturesCode($userId, $vin, $lcdv, $vehicleType, $corvertAttributs, null, null, false, $userDbId);

            // get existing NON FDS feature codes
            $existingNonFDSFeatures = [];
            $currentFeatureCodes = $vehicle['featureCode'] ?? [];
            $nonFdsKeys = FeatureCode::getNonFdsFeatureKeys();
            foreach ($currentFeatureCodes as $featureCode) {
                if (in_array($featureCode['code'], $nonFdsKeys)) {
                    $existingNonFDSFeatures[] = $featureCode;
                }
            }

            $featureCodes = array_merge($featureCodes, $existingNonFDSFeatures);
            
            // Update the vehicle document with new feature codes and expiry
            $this->userDataService->updateFeatureCodes($userId, $vin, $featureCodes);
        }
        return $featureCodes;
    }

    /**
     * Convert ODM UserData document to legacy UserDataDocument for compatibility
     * with XFVehicleRefreshService and XPVehicleRefreshService
     */
    private function convertODMToLegacyUserDataDocument($odmUserData, string $userId): LegacyUserDataDocument
    {
        $userDataDocument = new LegacyUserDataDocument();
        $userDataDocument->userId = $userId;

        if ($odmUserData) {
            // Convert ODM data to legacy format if needed
            // For now, we only need the userId which is already set
            $this->logger->debug(__METHOD__ . ' Converted ODM UserData to legacy UserDataDocument', [
                'userId' => $userId,
                'odmUserDataExists' => true
            ]);
        } else {
            $this->logger->debug(__METHOD__ . ' Created new legacy UserDataDocument for non-existing user', [
                'userId' => $userId,
                'odmUserDataExists' => false
            ]);
        }

        return $userDataDocument;
    }

    /**
     * Extract userDbId from ODM UserData document
     * Since ODM model doesn't have userDbId field, we'll use userId as fallback
     * or derive it from PSA ID if available
     */
    private function extractUserDbIdFromODMUserData($userData): ?string
    {
        if (!$userData) {
            return null;
        }

        // For now, use userId as userDbId since the ODM model doesn't have a separate userDbId field
        // In the future, this could be enhanced to derive from PSA ID or other fields if needed
        $userDbId = $userData->getUserDbId();

        $this->logger->debug(__METHOD__ . ' Extracted userDbId from ODM UserData', [
            'userId' => $userData->getUserId(),
            'userDbId' => $userDbId,
        ]);

        return $userDbId;
    }

    /**
     * Convert ODM Vehicle to legacy Vehicle format for backward compatibility
     */
    private function convertODMVehicleToLegacy(Vehicle $odmVehicle): LegacyVehicle
    {
        $legacyVehicle = new LegacyVehicle();

        // Map ODM Vehicle fields to legacy Vehicle properties
        $legacyVehicle->id = $odmVehicle->getVin() ?? ''; // Use VIN as ID
        $legacyVehicle->vin = $odmVehicle->getVin() ?? '';
        $legacyVehicle->brand = $odmVehicle->getBrand() ?? '';
        $legacyVehicle->shortLabel = $odmVehicle->getModel() ?? '';
        $legacyVehicle->modelDescription = $odmVehicle->getModel() ?? '';
        $legacyVehicle->versionId = $odmVehicle->getVersionId() ?? '';
        $legacyVehicle->sdp = $odmVehicle->getStatus() ?? ''; // ODM uses status field
        $legacyVehicle->picture = null; // ODM Vehicle doesn't have picture field
        $legacyVehicle->nickName = null; // ODM Vehicle doesn't have nickName field
        $legacyVehicle->isOrder = true; // Default value for backward compatibility
        $legacyVehicle->warrantyStartDate = null; // ODM Vehicle doesn't have this field
        $legacyVehicle->featureCode = $odmVehicle->getFeatureCode() ?? [];
        $legacyVehicle->vehicleOrder = null; // ODM Vehicle doesn't have vehicleOrder

        return $legacyVehicle;
    }

    /**
     * Extract order information from vehicle data
     * This method checks for order information in both ODM and legacy formats
     */
    private function extractOrderInformationFromVehicle(Vehicle $vehicle, string $userId): ?array
    {
        try {
            $this->logger->info(__METHOD__ . ' Starting order information extraction', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
            ]);

            // Method 1: Check if order information is stored in feature codes or other ODM fields
            // (This would be the case if order data was migrated to the ODM structure)
            $featureCodes = $vehicle->getFeatureCode() ?? [];
            $this->logger->info(__METHOD__ . ' Checking feature codes for order information', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
                'featureCodeCount' => count($featureCodes),
                'featureCodes' => array_map(function($fc) { return $fc['code'] ?? 'NO_CODE'; }, $featureCodes),
            ]);

            foreach ($featureCodes as $featureCode) {
                if (isset($featureCode['code']) && in_array($featureCode['code'], ['MOP_ID', 'ORDER_FORM_ID'])) {
                    // If order information is stored in feature codes, extract it
                    // This is a potential future enhancement
                    $this->logger->info(__METHOD__ . ' Found order-related feature code', [
                        'vin' => $vehicle->getVin(),
                        'userId' => $userId,
                        'featureCode' => $featureCode,
                    ]);
                }
            }

            // Method 2: Check raw MongoDB document for legacy vehicleOrder data
            // Enhanced to handle null VINs with fallback logic
            $vin = $vehicle->getVin();

            $this->logger->info(__METHOD__ . ' Attempting to get raw vehicle order data', [
                'vin' => $vin,
                'userId' => $userId,
                'vinIsNull' => $vin === null,
            ]);

            // Try to get order data from raw MongoDB (handles null VINs with fallback)
            $rawOrderData = $this->userDataService->getRawVehicleOrderData($userId, $vin);

            $this->logger->info(__METHOD__ . ' Raw vehicle order data result', [
                'vin' => $vin,
                'userId' => $userId,
                'rawOrderDataExists' => $rawOrderData !== null,
                'rawOrderData' => $rawOrderData,
            ]);

            if ($rawOrderData) {
                $this->logger->info(__METHOD__ . ' Successfully found order data in raw MongoDB', [
                    'vin' => $vin,
                    'userId' => $userId,
                    'mopId' => $rawOrderData['mopId'] ?? null,
                    'orderFormId' => $rawOrderData['orderFormId'] ?? null,
                    'trackingStatus' => $rawOrderData['trackingStatus'] ?? null,
                    'orderFormStatus' => $rawOrderData['orderFormStatus'] ?? null,
                    'extractionMethod' => $vin ? 'VIN_match' : 'fallback_search',
                ]);
                return $rawOrderData;
            } else {
                $this->logger->warning(__METHOD__ . ' No order data found in raw MongoDB', [
                    'userId' => $userId,
                    'vin' => $vin,
                    'vehicleId' => $vehicle->getDocumentId() ?? 'unknown',
                ]);
            }

            // Method 3: Check if the vehicle is marked as an order (isOrder = true)
            // and try to derive order information from other sources
            $isOnOrder = $this->isVehicleOnOrder($vehicle);
            $this->logger->info(__METHOD__ . ' Checking if vehicle is on order', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
                'isOnOrder' => $isOnOrder,
            ]);

            if ($isOnOrder) {
                // If vehicle is on order but no order data found, it might be a data migration issue
                $this->logger->warning(__METHOD__ . ' Vehicle marked as order but no order data found', [
                    'vin' => $vehicle->getVin(),
                    'userId' => $userId,
                ]);
            }

            $this->logger->info(__METHOD__ . ' No order information found for vehicle', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
            ]);

            return null;
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__ . ' Error extracting order information', [
                'vin' => $vehicle->getVin(),
                'userId' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

}
